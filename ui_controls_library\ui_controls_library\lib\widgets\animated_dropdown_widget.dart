import 'package:flutter/material.dart';

/// A dropdown-style widget with animated background transitions
class AnimatedDropdownWidget extends StatefulWidget {
  /// List of options to display
  final List<String> options;
  
  /// Initially selected option
  final String? initialValue;
  
  /// Callback when selection changes
  final Function(String?)? onChanged;
  
  /// Height of each option item
  final double itemHeight;
  
  /// Background color for unselected items
  final Color backgroundColor;
  
  /// Background color for selected/hovered items
  final Color selectedBackgroundColor;
  
  /// Text color for unselected items
  final Color textColor;
  
  /// Text color for selected items
  final Color selectedTextColor;
  
  /// Border radius for the container
  final double borderRadius;
  
  /// Animation duration for transitions
  final Duration animationDuration;
  
  /// Whether to show border
  final bool showBorder;
  
  /// Border color
  final Color borderColor;
  
  /// Font size for text
  final double fontSize;
  
  /// Font weight for text
  final FontWeight fontWeight;

  const AnimatedDropdownWidget({
    super.key,
    required this.options,
    this.initialValue,
    this.onChanged,
    this.itemHeight = 48.0,
    this.backgroundColor = Colors.white,
    this.selectedBackgroundColor = const Color(0xFFE3F2FD),
    this.textColor = Colors.black87,
    this.selectedTextColor = const Color(0xFF1976D2),
    this.borderRadius = 8.0,
    this.animationDuration = const Duration(milliseconds: 300),
    this.showBorder = true,
    this.borderColor = const Color(0xFFE0E0E0),
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
  });

  @override
  State<AnimatedDropdownWidget> createState() => _AnimatedDropdownWidgetState();
}

class _AnimatedDropdownWidgetState extends State<AnimatedDropdownWidget>
    with TickerProviderStateMixin {
  String? _selectedValue;
  int? _hoveredIndex;
  late List<AnimationController> _animationControllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _selectedValue = widget.initialValue;
    
    // Create animation controllers for each option
    _animationControllers = List.generate(
      widget.options.length,
      (index) => AnimationController(
        duration: widget.animationDuration,
        vsync: this,
      ),
    );
    
    // Create animations for each option
    _animations = _animationControllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();
    
    // Animate the initially selected item
    if (_selectedValue != null) {
      final selectedIndex = widget.options.indexOf(_selectedValue!);
      if (selectedIndex != -1) {
        _animationControllers[selectedIndex].forward();
      }
    }
  }

  @override
  void dispose() {
    for (final controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _handleSelection(String option, int index) {
    setState(() {
      _selectedValue = option;
    });
    
    // Animate the selected item
    for (int i = 0; i < _animationControllers.length; i++) {
      if (i == index) {
        _animationControllers[i].forward();
      } else {
        _animationControllers[i].reverse();
      }
    }
    
    widget.onChanged?.call(option);
  }

  void _handleHover(int? index) {
    setState(() {
      _hoveredIndex = index;
    });
    
    // Animate hover effects
    for (int i = 0; i < _animationControllers.length; i++) {
      if (i == index && _selectedValue != widget.options[i]) {
        _animationControllers[i].forward();
      } else if (_selectedValue != widget.options[i]) {
        _animationControllers[i].reverse();
      }
    }
  }

  Widget _buildOption(String option, int index) {
    final isSelected = option == _selectedValue;
    final isHovered = index == _hoveredIndex;
    
    return AnimatedBuilder(
      animation: _animations[index],
      builder: (context, child) {
        return MouseRegion(
          onEnter: (_) => _handleHover(index),
          onExit: (_) => _handleHover(null),
          child: GestureDetector(
            onTap: () => _handleSelection(option, index),
            child: Container(
              height: widget.itemHeight,
              decoration: BoxDecoration(
                color: Color.lerp(
                  widget.backgroundColor,
                  widget.selectedBackgroundColor,
                  _animations[index].value,
                ),
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border: widget.showBorder
                    ? Border.all(color: widget.borderColor, width: 1.0)
                    : null,
              ),
              child: Stack(
                children: [
                  // Left-to-right transition effect
                  Positioned.fill(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(widget.borderRadius),
                      child: AnimatedContainer(
                        duration: widget.animationDuration,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            stops: [
                              0.0,
                              _animations[index].value,
                              _animations[index].value,
                              1.0,
                            ],
                            colors: [
                              widget.selectedBackgroundColor,
                              widget.selectedBackgroundColor,
                              widget.backgroundColor,
                              widget.backgroundColor,
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Text content
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        option,
                        style: TextStyle(
                          color: Color.lerp(
                            widget.textColor,
                            widget.selectedTextColor,
                            _animations[index].value,
                          ),
                          fontSize: widget.fontSize,
                          fontWeight: widget.fontWeight,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(widget.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: widget.options.asMap().entries.map((entry) {
          final index = entry.key;
          final option = entry.value;
          
          return Padding(
            padding: EdgeInsets.only(
              bottom: index < widget.options.length - 1 ? 2.0 : 0.0,
            ),
            child: _buildOption(option, index),
          );
        }).toList(),
      ),
    );
  }
}
