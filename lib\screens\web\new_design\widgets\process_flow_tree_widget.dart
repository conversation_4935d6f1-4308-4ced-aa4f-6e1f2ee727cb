import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/models/workflow/workflow_manual_response_model.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/theme/spacing.dart';

// class ProcessFlow {
//   String? loName;
//   String? actorType;
//   String? description;
//   String? routeType;
//   String? id;
//   String? goId;
//   String? loId;
//   List<Condition>? conditions;
//   List<String>? routes;
//   List<ParallelRoute>? parallelRoutes;
//   String? joinAt;

//   ProcessFlow({
//     this.loName,
//     this.actorType,
//     this.description,
//     this.routeType,
//     this.id,
//     this.goId,
//     this.loId,
//     this.conditions,
//     this.routes,
//     this.parallelRoutes,
//     this.joinAt,
//   });

//   factory ProcessFlow.fromJson(Map<String, dynamic> json) => ProcessFlow(
//         loName: json["lo_name"],
//         actorType: json["actor_type"],
//         description: json["description"],
//         routeType: json["route_type"],
//         id: json["id"],
//         goId: json["go_id"],
//         loId: json["lo_id"],
//         conditions: json["conditions"] == null
//             ? []
//             : List<Condition>.from(
//                 json["conditions"]!.map((x) => Condition.fromJson(x))),
//         routes: json["routes"] == null
//             ? []
//             : List<String>.from(json["routes"]!.map((x) => x)),
//         parallelRoutes: json["parallel_routes"] == null
//             ? []
//             : List<ParallelRoute>.from(
//                 json["parallel_routes"]!.map((x) => ParallelRoute.fromJson(x))),
//         joinAt: json["join_at"],
//       );
// }

// class Condition {
//   String? condition;
//   String? routeTo;

//   Condition({
//     this.condition,
//     this.routeTo,
//   });

//   factory Condition.fromJson(Map<String, dynamic> json) => Condition(
//         condition: json["condition"],
//         routeTo: json["route_to"],
//       );
// }

// class ParallelRoute {
//   String? routeTo;
//   String? description;

//   ParallelRoute({
//     this.routeTo,
//     this.description,
//   });

//   factory ParallelRoute.fromJson(Map<String, dynamic> json) => ParallelRoute(
//         routeTo: json["route_to"],
//         description: json["description"],
//       );
// }

// Individual node widget
class NodeWidget {
  final String text;
  final NodeType nodeType;
  final double leftPadding;
  final bool isLast;

  NodeWidget({
    required this.text,
    required this.nodeType,
    this.leftPadding = 0,
    this.isLast = false,
  });
}

enum NodeType {
  root, // SubmitLeaveRequest
  sequential, // NotifyEmployee, UpdateCalendar, etc.
  parallel, // Same as sequential but in parallel context
  alternate, // Same as sequential but in alternate context
  prefixedNode, // Alt1. UploadDocumentation, Parl1. UpdateCalendar
  terminal, // X
  arrow, // ->
}

// Global variables for tracking
Set<String> expandedNodeNames = {};
Map<String, List<String>> nodeOccurrences = {};

// Global colors for parallel nodes
List<Color> colors = [
  Color(0xFFE3F2FD),
  Color(0xffFFFDD0),
  Color(0xffF5F5DC),
  Color(0xffAAF0D1),
  Color(0xffAAF0D1),
  Color(0xffF0FFF0),
  Color(0xffB0EACD),
  Color(0xffACE1AF),
  Color(0xffD0F0C0),
  Color(0xffADDFAD),
  Color(0xffA8E4A0),
  Color(0xffD5F5B8),
  Color(0xffCDE5B2),
  Color(0xffECEBBD),
  Color(0xffB2FFFF),
  Color(0xffAFEEEE),
  Color(0xffF5FFFA),
  Color(0xffA7D8C9),
  Color(0xff7FFFD4),
  Color(0xffCFFFE5),
  Color(0xffD4F1F9),
  Color(0xffD6F1FF),
  Color(0xffAEC6CF),
];

// Track parallel group colors by parent node
Map<String, Color> parallelGroupColors = {};
int parallelColorIndex = 0;
// List colors = ManualCreationProvider.pastelColors;

// Track which parent nodes have parallel children
Map<String, String> parallelParentMap = {};

// Track duplicate node colors for link icons
Map<String, Color> duplicateNodeColors = {};
int duplicateColorIndex = 0;

// Track actual occurrences in rendered tree (not just possible paths)
Map<String, int> renderedNodeCount = {};

// Separate color palette for link icons to avoid overlap with parallel colors
List<Color> linkIconColors = [
  // Color(0xffFFA07A),
  Color(0xffDFFF00),
  Color(0xffE0B0FF),
  Color(0xffB4A7D6),
  Color(0xffC7E9F1),
  Color(0xffC3CDE6),
  Color(0xffCCCCFF),
  Color(0xffE6E6FA),
  Color(0xffE4D0EC),
  Color(0xffD8BFD8),
  Color(0xffB0E0E6),
  Color(0xffF2D1E3),
  Color(0xffF49AC2),
  Color(0xffFFA6C9),
  Color(0xffFBB1BD),
  Color(0xffFFD1DC),
  Color(0xffFFDFDD),
  Color(0xffFFDAB9),
  Color(0xffFDC5B5),
  Color(0xffD8B7DD),
];

class ProcessFlowWidget extends StatelessWidget {
  final List<ProcessFlow> processList;

  const ProcessFlowWidget({Key? key, required this.processList})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final treeStructure = buildTreeStructure(processList);

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: treeStructure.map((row) => buildRowWidget(row)).toList(),
          ),
        ),
      ),
    );
  }

  List<List<NodeWidget>> buildTreeStructure(List<ProcessFlow> processList) {
    // Reset global variables
    expandedNodeNames.clear();
    nodeOccurrences.clear();
    parallelParentMap.clear();
    parallelGroupColors.clear();
    parallelColorIndex = 0;
    renderedNodeCount.clear();

    final nodeMapById = <String, ProcessFlow>{};
    final Set<String> nextNodeIds = {};

    for (final node in processList) {
      nodeMapById[node.loId!] = node;

      if (node.conditions != null) {
        for (final condition in node.conditions!) {
          if (condition.routeTo != "Terminal") {
            nextNodeIds.add(condition.routeTo!);
          }
        }
      }

      if (node.routes != null) {
        for (final route in node.routes!) {
          if (route != "Terminal") {
            nextNodeIds.add(route);
          }
        }
      }

      if (node.parallelRoutes != null) {
        for (final pr in node.parallelRoutes!) {
          if (pr.routeTo != "Terminal") {
            nextNodeIds.add(pr.routeTo!);
          }
        }
      }

      if (node.joinAt != null && node.joinAt != "Terminal") {
        nextNodeIds.add(node.joinAt!);
      }
    }

    // Pre-process parallel relationships
    for (final node in processList) {
      if (node.routeType == "Parallel" && node.parallelRoutes != null) {
        final parentNodeName = node.loName!;
        int parCount = 1;
        for (final pr in node.parallelRoutes!) {
          if (pr.routeTo != "Terminal") {
            parallelParentMap['Parl$parCount'] = parentNodeName;
            parCount++;
          }
        }

        // Assign color to this parallel group immediately
        if (!parallelGroupColors.containsKey(parentNodeName)) {
          parallelGroupColors[parentNodeName] =
              colors[parallelColorIndex % colors.length];
          parallelColorIndex++;
        }
      }
    }

    final rootNodes =
        processList.where((node) => !nextNodeIds.contains(node.loId)).toList();

    // First pass: collect all occurrences
    for (final root in rootNodes) {
      collectOccurrences(root, nodeMapById, []);
    }

    // Second pass: build tree structure
    List<List<NodeWidget>> result = [];
    for (final root in rootNodes) {
      final rows = buildNodeTreeStructure(root, 0, nodeMapById, [], '');
      result.addAll(rows);
    }

    return result;
  }

  void collectOccurrences(
    ProcessFlow node,
    Map<String, ProcessFlow> nodeMap,
    List<String> currentPath,
  ) {
    final nodeId = node.loId!;
    final nodeName = node.loName!;

    if (currentPath.contains(nodeId)) {
      return;
    }

    if (!nodeOccurrences.containsKey(nodeName)) {
      nodeOccurrences[nodeName] = [];
    }
    final pathKey = '${currentPath.join('/')}/$nodeId';
    nodeOccurrences[nodeName]!.add(pathKey);

    currentPath.add(nodeId);

    if (node.routeType == "Sequential" && node.routes != null) {
      for (final next in node.routes!) {
        if (next != "Terminal" && nodeMap.containsKey(next)) {
          collectOccurrences(nodeMap[next]!, nodeMap, List.from(currentPath));
        }
      }
    } else if (node.routeType == "Alternate" && node.conditions != null) {
      for (final cond in node.conditions!) {
        if (cond.routeTo != "Terminal" && nodeMap.containsKey(cond.routeTo)) {
          collectOccurrences(
              nodeMap[cond.routeTo]!, nodeMap, List.from(currentPath));
        }
      }
    } else if (node.routeType == "Parallel" && node.parallelRoutes != null) {
      for (final pr in node.parallelRoutes!) {
        if (pr.routeTo != "Terminal" && nodeMap.containsKey(pr.routeTo)) {
          collectOccurrences(
              nodeMap[pr.routeTo]!, nodeMap, List.from(currentPath));
        }
      }

      if (node.joinAt != null && node.joinAt != "Terminal") {
        if (nodeMap.containsKey(node.joinAt)) {
          collectOccurrences(
              nodeMap[node.joinAt]!, nodeMap, List.from(currentPath));
        }
      }
    }

    currentPath.removeLast();
  }

  // Helper method to track rendered nodes
  void incrementRenderedNodeCount(String nodeName) {
    renderedNodeCount[nodeName] = (renderedNodeCount[nodeName] ?? 0) + 1;
  }

  // Dynamic method to check if a node is a duplicate based on its actual rendering context
  bool _isDynamicDuplicate(String nodeName, List<String> currentTreePath) {
    // Get the current count of how many times this node has been rendered
    int currentCount = renderedNodeCount[nodeName] ?? 0;

    // Get total possible occurrences from path analysis
    int totalPossibleOccurrences = nodeOccurrences[nodeName]?.length ?? 1;

    // A node is considered a duplicate if:
    // 1. It appears in multiple possible paths AND
    // 2. We're not at the last occurrence in the current rendering context
    bool hasMultiplePossiblePaths = totalPossibleOccurrences > 1;
    bool isLastOccurrenceInPath = isLastOccurrence(nodeName, currentTreePath);

    return hasMultiplePossiblePaths && !isLastOccurrenceInPath;
  }

  // Dynamic method to check if a node is expanding based on context
  bool _isDynamicExpanding(String nodeName, String displayText,
      List<String> currentTreePath, Map<String, ProcessFlow> nodeMap) {
    // If the text has % prefix, it means this is an expanding duplicate
    if (displayText.startsWith('%')) {
      return true;
    }

    // If the text has % suffix, it means this is a non-expanding duplicate
    if (displayText.endsWith('%')) {
      return false;
    }

    // Check if the node has children that would be expanded
    bool hasExpandableChildren = false;

    // Find the node in the map
    ProcessFlow? nodeFlow;
    for (var node in nodeMap.values) {
      if (node.loName == nodeName) {
        nodeFlow = node;
        break;
      }
    }

    if (nodeFlow != null) {
      // Check if it has routes, conditions, or parallel routes
      hasExpandableChildren = (nodeFlow.routes != null &&
              nodeFlow.routes!.isNotEmpty &&
              !nodeFlow.routes!.every((r) => r == "Terminal")) ||
          (nodeFlow.conditions != null &&
              nodeFlow.conditions!.isNotEmpty &&
              !nodeFlow.conditions!.every((c) => c.routeTo == "Terminal")) ||
          (nodeFlow.parallelRoutes != null &&
              nodeFlow.parallelRoutes!.isNotEmpty &&
              !nodeFlow.parallelRoutes!
                  .every((pr) => pr.routeTo == "Terminal"));
    }

    // A node is expanding if it's in expandedNodeNames and has expandable children
    return expandedNodeNames.contains(nodeName) && hasExpandableChildren;
  }

  // Helper method to extract clean node name from display text
  String _extractNodeName(String text) {
    String nodeName = text;

    // Remove prefixes like "Alt1. " or "Parl1. "
    if (text.contains('. ')) {
      nodeName = text.split('. ').last;
    }

    // Remove % prefix if present
    if (nodeName.startsWith('%')) {
      nodeName = nodeName.substring(1);
    }

    // Remove % suffix if present
    if (nodeName.endsWith('%')) {
      nodeName = nodeName.substring(0, nodeName.length - 1);
    }

    return nodeName;
  }

  // Dynamic method to check if a node is duplicate based on rendered count
  bool _isDuplicateFromRenderedCount(String nodeName) {
    // A node is considered duplicate if it has been rendered more than once
    // or if it has % markers indicating duplication
    int renderedCount = renderedNodeCount[nodeName] ?? 0;
    int possibleOccurrences = nodeOccurrences[nodeName]?.length ?? 1;

    return possibleOccurrences > 1;
  }

  // Dynamic method to check if a node is expanding from context
  bool _isExpandingFromContext(String displayText, String nodeName) {
    // If the text has % prefix, it means this is an expanding duplicate
    if (displayText.startsWith('%')) {
      return true;
    }

    // If the text has % suffix, it means this is a non-expanding duplicate
    if (displayText.endsWith('%')) {
      return false;
    }

    // For nodes without % markers, check if they are in expandedNodeNames
    return expandedNodeNames.contains(nodeName);
  }

  List<NodeWidget> parseSequentialChain(
      String chainString, double basePadding, String prefix) {
    List<NodeWidget> nodes = [];

    // Remove prefix from chain string if it exists
    String cleanChain = chainString;
    if (prefix.isNotEmpty && chainString.startsWith(prefix)) {
      cleanChain = chainString.substring(prefix.length).trim();
    }

    // Split the chain by " -> "
    final parts = cleanChain.split(' -> ');

    for (int i = 0; i < parts.length; i++) {
      final part = parts[i].trim();

      if (part.isEmpty) continue;

      if (part == 'X') {
        nodes.add(NodeWidget(
          text: 'X',
          nodeType: NodeType.terminal,
          leftPadding: 0,
        ));
      } else {
        // Determine node type and clean text
        String cleanText = part;
        NodeType nodeType = NodeType.sequential;

        if (part.startsWith('%')) {
          cleanText = part.substring(1);
        }

        // Combine prefix with first node if this is the first node and prefix exists
        String displayText = cleanText;
        if (i == 0 && prefix.isNotEmpty) {
          displayText = prefix.trim() + cleanText;
          nodeType = NodeType.prefixedNode;
        }

        // Track rendered node count
        String nodeName = cleanText;
        if (nodeName.endsWith('%')) {
          nodeName = nodeName.substring(0, nodeName.length - 1);
        }
        incrementRenderedNodeCount(nodeName);

        nodes.add(NodeWidget(
          text: displayText,
          nodeType: nodeType,
          leftPadding: i == 0 ? basePadding : 0,
        ));
      }
    }

    return nodes;
  }

  String buildSequentialChainString(
    ProcessFlow node,
    Map<String, ProcessFlow> nodeMap,
    List<String> currentPathVisited,
    List<ProcessFlow> chainNodes,
    bool isFirstExpansionOfHead,
    List<String> currentTreePath,
  ) {
    final nodeName = node.loName!;

    if (currentPathVisited.contains(nodeName)) {
      return '$nodeName (cycle)';
    }

    currentPathVisited.add(nodeName);
    chainNodes.add(node);

    // Dynamic duplicate detection
    bool isDuplicate = _isDynamicDuplicate(nodeName, currentTreePath);
    bool isLastOccurrenceOfNode = isLastOccurrence(nodeName, currentTreePath);
    bool isExpandingNode = expandedNodeNames.contains(nodeName);

    String nodeDisplay = nodeName;
    if (isDuplicate && !isLastOccurrenceOfNode) {
      if (isExpandingNode) {
        // Expanding duplicate nodes: % as prefix
        nodeDisplay = '%$nodeName';
      } else {
        // Non-expanding duplicate nodes: % as suffix
        nodeDisplay = '$nodeName%';
      }
    }

    // Check if node is terminal (no routes and no conditions)
    bool isTerminalNode = (node.routes == null || node.routes!.isEmpty) &&
        (node.conditions == null || node.conditions!.isEmpty) &&
        (node.parallelRoutes == null || node.parallelRoutes!.isEmpty);

    if (isTerminalNode) {
      return '$nodeDisplay -> X';
    }

    if (node.routeType == "Sequential" &&
        node.routes != null &&
        node.routes!.isNotEmpty) {
      final nextRoute = node.routes!.first;
      if (nextRoute == "Terminal") {
        return '$nodeDisplay -> X';
      } else if (nodeMap.containsKey(nextRoute)) {
        final nextChainString = buildSequentialChainString(
          nodeMap[nextRoute]!,
          nodeMap,
          currentPathVisited,
          chainNodes,
          isFirstExpansionOfHead,
          currentTreePath,
        );
        return '$nodeDisplay -> $nextChainString';
      }
    } else if (node.routeType == "Parallel" &&
        node.joinAt != null &&
        node.joinAt != "Terminal") {
      bool isLastOcc = isLastOccurrence(nodeName, currentTreePath);
      if (isLastOcc && nodeMap.containsKey(node.joinAt)) {
        final joinChainString = buildSequentialChainString(
          nodeMap[node.joinAt]!,
          nodeMap,
          currentPathVisited,
          chainNodes,
          isFirstExpansionOfHead,
          currentTreePath,
        );
        return '$nodeDisplay -> $joinChainString';
      }
    }

    return nodeDisplay;
  }

  bool isLastOccurrence(String nodeName, List<String> currentPath) {
    if (!nodeOccurrences.containsKey(nodeName)) return true;

    final pathKey = currentPath.join('/');
    final occurrences = nodeOccurrences[nodeName]!;

    // Check if current path matches the last occurrence
    return occurrences.isNotEmpty && occurrences.last.contains(pathKey);
  }

  List<List<NodeWidget>> buildNodeTreeStructure(
    ProcessFlow node,
    double depth,
    Map<String, ProcessFlow> nodeMap,
    List<String> currentPathForCycleDetection,
    String branchPrefix,
  ) {
    List<List<NodeWidget>> result = [];
    final nodeId = node.loId!;
    final nodeName = node.loName!;

    if (currentPathForCycleDetection.contains(nodeId)) {
      result.add([
        NodeWidget(
          text: '$branchPrefix$nodeName (cycle)',
          nodeType: NodeType.sequential,
          leftPadding: depth * 20,
        )
      ]);
      return result;
    }

    bool isFirstExpansionOfNode = !expandedNodeNames.contains(nodeName);
    bool isLastOccurrenceOfNode =
        isLastOccurrence(nodeName, currentPathForCycleDetection);

    currentPathForCycleDetection.add(nodeId);

    if (node.routeType == "Sequential") {
      final chainNodes = <ProcessFlow>[];
      final chainVisitedNames = <String>[];
      final chainString = buildSequentialChainString(
        node,
        nodeMap,
        chainVisitedNames,
        chainNodes,
        isFirstExpansionOfNode,
        currentPathForCycleDetection,
      );

      // Parse sequential chain into individual nodes
      final rowNodes =
          parseSequentialChain(chainString, depth * 20, branchPrefix);
      if (rowNodes.isNotEmpty) {
        result.add(rowNodes);
      }

      // Process children of sequential chains
      if (chainNodes.isNotEmpty && !chainString.endsWith('X')) {
        final lastNodeInChain = chainNodes.last;
        final lastNodeName = lastNodeInChain.loName!;

        bool shouldExpandLastNode = !expandedNodeNames.contains(lastNodeName) &&
            isLastOccurrence(lastNodeName, currentPathForCycleDetection);

        if (shouldExpandLastNode) {
          for (final chainNode in chainNodes) {
            expandedNodeNames.add(chainNode.loName!);
          }

          final children = buildNodeChildrenStructure(lastNodeInChain, depth,
              nodeMap, List.from(currentPathForCycleDetection), 0);
          result.addAll(children);
        }
      } else if (chainNodes.isNotEmpty && chainString.endsWith('X')) {
        if (chainNodes.length >= 2) {
          final lastNonTerminalNode = chainNodes[chainNodes.length - 2];
          final lastNonTerminalName = lastNonTerminalNode.loName!;

          bool shouldExpandLastNonTerminal =
              !expandedNodeNames.contains(lastNonTerminalName) &&
                  isLastOccurrence(
                      lastNonTerminalName, currentPathForCycleDetection);

          if (shouldExpandLastNonTerminal) {
            for (final chainNode in chainNodes) {
              expandedNodeNames.add(chainNode.loName!);
            }

            final chainStringUpToNode = chainString.substring(
                0,
                chainString.indexOf(lastNonTerminalName) +
                    lastNonTerminalName.length);
            final nodePositionInString = chainStringUpToNode.length;

            final children = buildNodeChildrenStructureWithAlignment(
                lastNonTerminalNode,
                depth,
                nodeMap,
                List.from(currentPathForCycleDetection),
                nodePositionInString.toDouble());
            result.addAll(children);
          }
        } else if (chainNodes.isNotEmpty && isFirstExpansionOfNode) {
          for (final chainNode in chainNodes) {
            expandedNodeNames.add(chainNode.loName!);
          }
        }
      }
    } else {
      String nodeDisplay = nodeName;
      if (node.loName == "Terminal") {
        nodeDisplay += ' X';
      }

      NodeType nodeType =
          branchPrefix.isNotEmpty ? NodeType.prefixedNode : NodeType.sequential;
      String displayText =
          branchPrefix.isNotEmpty ? '$branchPrefix$nodeDisplay' : nodeDisplay;

      // Dynamic duplicate detection
      bool isDuplicate =
          _isDynamicDuplicate(nodeName, currentPathForCycleDetection);
      bool isLastOccurrenceOfNode =
          isLastOccurrence(nodeName, currentPathForCycleDetection);
      bool isExpandingNode = expandedNodeNames.contains(nodeName);

      String finalDisplayText = displayText;
      if (isDuplicate && !isLastOccurrenceOfNode) {
        if (isExpandingNode) {
          // Expanding duplicate nodes: % as prefix
          finalDisplayText = '%$displayText';
        } else {
          // Non-expanding duplicate nodes: % as suffix
          finalDisplayText = '$displayText%';
        }
      }

      // Track rendered node count
      incrementRenderedNodeCount(nodeName);

      result.add([
        NodeWidget(
          text: finalDisplayText,
          nodeType: nodeType,
          leftPadding: depth * 20,
        )
      ]);

      if (isLastOccurrenceOfNode) {
        expandedNodeNames.add(nodeName);
        final children = buildNodeChildrenStructure(
            node, depth, nodeMap, List.from(currentPathForCycleDetection), 0);
        result.addAll(children);
      } else {
        expandedNodeNames.add(nodeName);
      }
    }

    currentPathForCycleDetection.removeLast();
    return result;
  }

  List<List<NodeWidget>> buildNodeChildrenStructure(
    ProcessFlow node,
    double depth,
    Map<String, ProcessFlow> nodeMap,
    List<String> currentPathForCycleDetection,
    double alignmentOffset,
  ) {
    return buildNodeChildrenStructureWithAlignment(
        node, depth, nodeMap, currentPathForCycleDetection, alignmentOffset);
  }

  List<List<NodeWidget>> buildNodeChildrenStructureWithAlignment(
    ProcessFlow node,
    double depth,
    Map<String, ProcessFlow> nodeMap,
    List<String> currentPathForCycleDetection,
    double alignmentOffset,
  ) {
    List<List<NodeWidget>> children = [];

    if (node.routeType == "Alternate" && node.conditions != null) {
      int altCount = 1;
      for (int i = 0; i < node.conditions!.length; i++) {
        final cond = node.conditions![i];
        bool isLastAlternate = (i == node.conditions!.length - 1);

        if (cond.routeTo == "Terminal") {
          children.add([
            NodeWidget(
              text: 'Alt$altCount. X',
              nodeType: NodeType.prefixedNode,
              leftPadding: (depth + 1) * 20 + alignmentOffset,
              isLast: isLastAlternate,
            ),
          ]);
        } else if (nodeMap.containsKey(cond.routeTo)) {
          final childRows = buildNodeTreeStructureWithAlignment(
            nodeMap[cond.routeTo]!,
            depth + 1,
            nodeMap,
            List.from(currentPathForCycleDetection),
            'Alt$altCount. ',
            alignmentOffset,
          );
          // Update the first row of childRows to set isLast property
          if (childRows.isNotEmpty && childRows[0].isNotEmpty) {
            childRows[0][0] = NodeWidget(
              text: childRows[0][0].text,
              nodeType: childRows[0][0].nodeType,
              leftPadding: childRows[0][0].leftPadding,
              isLast: isLastAlternate,
            );
          }
          children.addAll(childRows);
        } else {
          // If routeTo node doesn't exist, treat as terminal
          children.add([
            NodeWidget(
              text: 'Alt$altCount. X',
              nodeType: NodeType.prefixedNode,
              leftPadding: (depth + 1) * 20 + alignmentOffset,
              isLast: isLastAlternate,
            ),
          ]);
        }
        altCount++;
      }
    } else if (node.routeType == "Parallel" && node.parallelRoutes != null) {
      // Store the parent node name for parallel children
      final parentNodeName = node.loName!;

      int parCount = 1;
      for (final pr in node.parallelRoutes!) {
        if (pr.routeTo != "Terminal" && nodeMap.containsKey(pr.routeTo)) {
          // Store the mapping of parallel children to their parent
          parallelParentMap['Parl$parCount'] = parentNodeName;

          final childRows = buildNodeTreeStructureWithAlignment(
            nodeMap[pr.routeTo]!,
            depth + 1,
            nodeMap,
            List.from(currentPathForCycleDetection),
            'Parl$parCount. ',
            0, // Reset alignmentOffset to 0 for parallel children
          );
          children.addAll(childRows);
        } else {
          // If routeTo is Terminal or node doesn't exist, treat as terminal
          final terminalPadding = (depth + 1) * 20;
          children.add([
            NodeWidget(
              text: 'Parl$parCount. X',
              nodeType: NodeType.prefixedNode,
              leftPadding: terminalPadding,
            ),
          ]);
        }
        parCount++;
      }
    }

    return children;
  }

  List<List<NodeWidget>> buildNodeTreeStructureWithAlignment(
    ProcessFlow node,
    double depth,
    Map<String, ProcessFlow> nodeMap,
    List<String> currentPathForCycleDetection,
    String branchPrefix,
    double alignmentOffset,
  ) {
    List<List<NodeWidget>> result = [];
    final nodeId = node.loId!;
    final nodeName = node.loName!;

    if (currentPathForCycleDetection.contains(nodeId)) {
      result.add([
        NodeWidget(
          text: '$branchPrefix$nodeName (cycle)',
          nodeType: NodeType.prefixedNode,
          leftPadding: depth * 20 + alignmentOffset,
        )
      ]);
      return result;
    }

    bool isFirstExpansionOfNode = !expandedNodeNames.contains(nodeName);

    currentPathForCycleDetection.add(nodeId);

    if (node.routeType == "Sequential") {
      final chainNodes = <ProcessFlow>[];
      final chainVisitedNames = <String>[];
      final chainString = buildSequentialChainString(
        node,
        nodeMap,
        chainVisitedNames,
        chainNodes,
        isFirstExpansionOfNode,
        currentPathForCycleDetection,
      );

      final rowNodes = parseSequentialChain(
          chainString, depth * 20 + alignmentOffset, branchPrefix);

      result.add(rowNodes);

      // Process children similar to main function
      if (chainNodes.isNotEmpty && !chainString.endsWith('X')) {
        final lastNodeInChain = chainNodes.last;
        final lastNodeName = lastNodeInChain.loName!;

        bool shouldExpandLastNode = !expandedNodeNames.contains(lastNodeName) &&
            isLastOccurrence(lastNodeName, currentPathForCycleDetection);

        if (shouldExpandLastNode) {
          for (final chainNode in chainNodes) {
            expandedNodeNames.add(chainNode.loName!);
          }

          final children = buildNodeChildrenStructureWithAlignment(
              lastNodeInChain,
              depth,
              nodeMap,
              List.from(currentPathForCycleDetection),
              alignmentOffset);
          result.addAll(children);
        }
      } else if (chainNodes.isNotEmpty && chainString.endsWith('X')) {
        if (chainNodes.length >= 2) {
          final lastNonTerminalNode = chainNodes[chainNodes.length - 2];
          final lastNonTerminalName = lastNonTerminalNode.loName!;

          bool shouldExpandLastNonTerminal =
              !expandedNodeNames.contains(lastNonTerminalName) &&
                  isLastOccurrence(
                      lastNonTerminalName, currentPathForCycleDetection);

          if (shouldExpandLastNonTerminal) {
            for (final chainNode in chainNodes) {
              expandedNodeNames.add(chainNode.loName!);
            }

            final chainStringUpToNode = chainString.substring(
                0,
                chainString.indexOf(lastNonTerminalName) +
                    lastNonTerminalName.length);
            final nodePositionInString = chainStringUpToNode.length;

            final children = buildNodeChildrenStructureWithAlignment(
                lastNonTerminalNode,
                depth,
                nodeMap,
                List.from(currentPathForCycleDetection),
                alignmentOffset + nodePositionInString);
            result.addAll(children);
          }
        }
      }
    } else {
      String nodeDisplay = nodeName;
      if (node.loName == "Terminal") {
        nodeDisplay += ' X';
      }

      String displayText = branchPrefix + nodeDisplay;
      NodeType nodeType = NodeType.prefixedNode;

      // Dynamic duplicate detection
      bool isDuplicate =
          _isDynamicDuplicate(nodeName, currentPathForCycleDetection);
      bool isLastOccurrenceOfNode =
          isLastOccurrence(nodeName, currentPathForCycleDetection);
      bool isExpandingNode = expandedNodeNames.contains(nodeName);

      String finalDisplayText = displayText;
      if (isDuplicate && !isLastOccurrenceOfNode) {
        if (isExpandingNode) {
          // Expanding duplicate nodes: % as prefix
          finalDisplayText = '%$displayText';
        } else {
          // Non-expanding duplicate nodes: % as suffix
          finalDisplayText = '$displayText%';
        }
      }

      // Track rendered node count
      incrementRenderedNodeCount(nodeName);

      result.add([
        NodeWidget(
          text: finalDisplayText,
          nodeType: nodeType,
          leftPadding: depth * 20 + alignmentOffset,
        )
      ]);

      expandedNodeNames.add(nodeName);
      final children = buildNodeChildrenStructureWithAlignment(node, depth,
          nodeMap, List.from(currentPathForCycleDetection), alignmentOffset);
      result.addAll(children);
    }

    currentPathForCycleDetection.removeLast();
    return result;
  }

  Widget buildRowWidget(List<NodeWidget> rowNodes) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: rowNodes.map((node) => buildNodeWidget(node)).toList(),
      ),
    );
  }

  Widget buildAlternateNodeRow(
      String text, int altIndex, double leftPadding, bool isLast) {
    // Dynamic duplicate checking
    String nodeName = _extractNodeName(text);
    bool isDuplicate = _isDuplicateFromRenderedCount(nodeName);
    bool isExpanding = _isExpandingFromContext(text, nodeName);
    Color? duplicateColor =
        isDuplicate ? getDuplicateNodeColor(nodeName) : null;

    String cleanText = text.contains("%") ? text.replaceAll("%", "") : text;

    // Build content with link icons for duplicates
    List<Widget> contentWidgets = [];

    if (isDuplicate && isExpanding) {
      // For expanding duplicate nodes, add link icon before text
      contentWidgets.add(_buildLinkIcon(boxColor: duplicateColor));
      contentWidgets.add(SizedBox(width: 4));
    }

    contentWidgets.add(Text(
      cleanText,
      style: TextStyle(
        fontFamily: 'monospace',
        fontSize: 12.0,
        color: Colors.black87,
        fontWeight: FontWeight.w600,
      ),
    ));

    if (isDuplicate && !isExpanding) {
      // For non-expanding duplicate nodes, add link icon after text
      contentWidgets.add(SizedBox(width: 4));
      contentWidgets.add(_buildLinkIcon(boxColor: duplicateColor));
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Left padding
          SizedBox(width: leftPadding),

          // Tree connection lines container
          SizedBox(
            width: 24,
            height: 28,
            child: CustomPaint(
              painter: AlternateTreeLinePainter(
                isLast: isLast,
                isFirst: altIndex == 1,
                color: Color(0xff797676),
              ),
              size: Size(24, 28),
            ),
          ),

          // Circle
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Color(0xff797676), width: 1.0),
              color: Colors.white,
            ),
          ),

          SizedBox(width: 8),

          // Text content with link icons
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            decoration: BoxDecoration(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(4.0),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: contentWidgets,
            ),
          ),
        ],
      ),
    );
  }

  Color? getParallelNodeColor(String text) {
    // Check if this is a parallel node (starts with "Parl")
    if (text.startsWith('Parl')) {
      // Extract the parallel number from text (e.g., "Parl1. NodeName" -> "Parl1")
      final parlMatch = RegExp(r'Parl(\d+)\.').firstMatch(text);
      if (parlMatch != null) {
        final parlKey = 'Parl${parlMatch.group(1)!}';

        // Get the parent node name from the mapping
        String? parentNodeName = parallelParentMap[parlKey];

        if (parentNodeName != null) {
          // Use the parent node name as the group key
          String groupKey = parentNodeName;

          // If this parallel parent group doesn't have a color yet, assign one
          if (!parallelGroupColors.containsKey(groupKey)) {
            parallelGroupColors[groupKey] =
                colors[parallelColorIndex % colors.length];
            parallelColorIndex++;
          }

          return parallelGroupColors[groupKey];
        }
      }
    }

    // Check if this node is the parent of a parallel group
    for (String parentNodeName in parallelParentMap.values) {
      if (text == parentNodeName || text.contains(parentNodeName)) {
        // If this is a parent node that has parallel children, give it the same color
        if (parallelGroupColors.containsKey(parentNodeName)) {
          return parallelGroupColors[parentNodeName];
        }
      }
    }

    // Check if this node is a child of a parallel group by checking if it contains parallel prefix
    for (String parlKey in parallelParentMap.keys) {
      if (text.startsWith(parlKey + '.')) {
        String? parentNodeName = parallelParentMap[parlKey];
        if (parentNodeName != null &&
            parallelGroupColors.containsKey(parentNodeName)) {
          return parallelGroupColors[parentNodeName];
        }
      }
    }

    return null;
  }

  bool isDuplicateNode(String text) {
    String nodeName = _extractNodeName(text);
    return _isDuplicateFromRenderedCount(nodeName);
  }

  bool isExpandingNode(String text) {
    String nodeName = _extractNodeName(text);
    return _isExpandingFromContext(text, nodeName);
  }

  Color getDuplicateNodeColor(String text) {
    String nodeName = _extractNodeName(text);

    // Assign a consistent color for all duplicates of this node using separate color palette
    if (!duplicateNodeColors.containsKey(nodeName)) {
      duplicateNodeColors[nodeName] =
          linkIconColors[duplicateColorIndex % linkIconColors.length];
      duplicateColorIndex++;
    }

    return duplicateNodeColors[nodeName]!;
  }

  Widget buildNodeWidget(NodeWidget node) {
    if (node.nodeType == NodeType.terminal) {
      return _buildTerminalIcon();
    }

    // Handle prefixed nodes that start with "Alt" with special dotted line design
    if (node.nodeType == NodeType.prefixedNode && node.text.startsWith('Alt')) {
      // Extract alt number from text (e.g., "Alt1. NodeName" -> 1)
      final altMatch = RegExp(r'Alt(\d+)\.').firstMatch(node.text);
      final altIndex = altMatch != null ? int.parse(altMatch.group(1)!) : 1;

      // Pass the isLast property from the NodeWidget
      return buildAlternateNodeRow(
          node.text, altIndex, node.leftPadding, node.isLast);
    }

    // Check if this is a duplicate node
    bool isDuplicate = isDuplicateNode(node.text);
    bool isExpanding = isExpandingNode(node.text);

    // Get parallel node color if applicable
    Color? parallelColor = getParallelNodeColor(node.text);

    // Get duplicate node color if applicable
    Color? duplicateColor =
        isDuplicate ? getDuplicateNodeColor(node.text) : null;

    // Remove all background colors and borders - use transparent/no styling
    Color backgroundColor = parallelColor ?? Colors.transparent;
    Color? borderColor = parallelColor != null ? parallelColor : null;
    Color textColor = Colors.black87; // Use consistent text color

    // Build the node content with link icons for duplicates
    List<Widget> nodeContent = [];

    if (isDuplicate && isExpanding) {
      // For expanding duplicate nodes, add link icon before text
      nodeContent.add(_buildLinkIcon(boxColor: duplicateColor));
      nodeContent.add(SizedBox(width: 4));
    }
    String cleanText =
        node.text.contains("%") ? node.text.replaceAll("%", "") : node.text;
    nodeContent.add(Text(
      cleanText,
      style: TextStyle(
        fontFamily: 'monospace',
        fontSize: 12.0,
        color: textColor,
        fontWeight: FontWeight.w600,
      ),
    ));

    if (isDuplicate && !isExpanding) {
      // For non-expanding duplicate nodes, add link icon after text
      nodeContent.add(SizedBox(width: 4));
      nodeContent.add(_buildLinkIcon(boxColor: duplicateColor));
    }

    return Container(
      margin: EdgeInsets.only(
        left: node.leftPadding,
        right: node.nodeType == NodeType.arrow ? 4.0 : 2.0,
      ),
      padding: node.nodeType == NodeType.arrow
          ? const EdgeInsets.symmetric(horizontal: 4.0, vertical: 2.0)
          : const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: backgroundColor,
        border: borderColor != null ? Border.all(color: borderColor) : null,
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: nodeContent,
      ),
    );
  }
}

// Build terminal icon for workflow end
Widget _buildTerminalIcon() {
  return Container(
    padding: EdgeInsets.all(4),
    child: Row(
      children: [
        SvgPicture.asset(
          'assets/images/workflow/terminal_icon.svg',
          width: 20,
          height: 20,
        ),
      ],
    ),
  );
}

// Build link icon for connections
Widget _buildLinkIcon({Color? boxColor}) {
  return Container(
    decoration:
        BoxDecoration(borderRadius: BorderRadius.circular(4), color: boxColor),
    padding: EdgeInsets.all(4),
    child: SvgPicture.asset(
      'assets/images/workflow/link_icon.svg',
      width: 20,
      height: 20,
      colorFilter: ColorFilter.mode(Colors.blue.shade600, BlendMode.srcIn),
    ),
  );
}

// Example usage widget
class ProcessFlowExample extends StatelessWidget {
  const ProcessFlowExample({super.key});

  @override
  Widget build(BuildContext context) {
    List<ProcessFlow> processList = [
      ProcessFlow(
          loName: "SubmitLeaveRequest",
          actorType: "HUMAN",
          description: "Employee submits a leave request",
          routeType: "Alternate",
          id: "GO1.PW1",
          goId: "GO1",
          loId: "GO1.LO1",
          conditions: [
            Condition(
                condition: "LeaveApplication.requiresDocumentation = true",
                routeTo: "GO1.LO2"),
            Condition(
                condition: "LeaveApplication.requiresDocumentation = false",
                routeTo: "GO1.LO3"),
          ]),
      ProcessFlow(
          loName: "UploadDocumentation",
          actorType: "HUMAN",
          description: "Employee uploads required documentation",
          routeType: "Sequential",
          id: "GO1.PW2",
          goId: "GO1",
          loId: "GO1.LO2",
          routes: ["GO1.LO3"]),
      ProcessFlow(
          loName: "ReviewLeaveRequest",
          actorType: "HUMAN",
          description: "Manager reviews the leave request",
          routeType: "Alternate",
          id: "GO1.PW3",
          goId: "GO1",
          loId: "GO1.LO3",
          conditions: [
            Condition(
                condition: "LeaveApplication.status = Approved",
                routeTo: "GO1.LO4"),
            Condition(
                condition: "LeaveApplication.status = Rejected",
                routeTo: "GO1.LO5"),
          ]),
      ProcessFlow(
          loName: "ApproveLeaveRequest",
          actorType: "SYSTEM",
          description: "System updates leave request status to approved",
          routeType: "Sequential",
          id: "GO1.PW4",
          goId: "GO1",
          loId: "GO1.LO4",
          routes: ["GO1.LO6"]),
      ProcessFlow(
          loName: "RejectLeaveRequest",
          actorType: "SYSTEM",
          description: "System updates leave request status to rejected",
          routeType: "Sequential",
          id: "GO1.PW5",
          goId: "GO1",
          loId: "GO1.LO5",
          routes: ["GO1.LO6"]),
      ProcessFlow(
        loName: "NotifyEmployee",
        actorType: "SYSTEM",
        description: "System notifies employee of the decision",
        routeType: "Parallel",
        id: "GO1.PW6",
        goId: "GO1",
        loId: "GO1.LO6",
        parallelRoutes: [
          ParallelRoute(
            routeTo: "GO1.LO7",
            description: "System updates employee calendar",
          ),
          ParallelRoute(
            routeTo: "GO1.LO9",
            description: "System logs audit trail for compliance",
          ),
        ],
        joinAt: "GO1.LO8",
      ),
      ProcessFlow(
          loName: "UpdateCalendar",
          actorType: "SYSTEM",
          description: "System updates employee calendar",
          routeType: "Alternate",
          id: "GO1.PW7",
          goId: "GO1",
          loId: "GO1.LO7",
          conditions: [
            Condition(
                condition: "LeaveApplication.status = Approved",
                routeTo: "GO1.LO8"),
            Condition(
                condition: "LeaveApplication.status = Rejected",
                routeTo: "Terminal"),
          ]),
      ProcessFlow(
          loName: "UpdateLeaveBalance",
          actorType: "SYSTEM",
          description: "System updates employee leave balance",
          routeType: "Sequential",
          id: "GO1.PW8",
          goId: "GO1",
          loId: "GO1.LO8",
          routes: ["Terminal"]),
      ProcessFlow(
          loName: "LogAuditTrail",
          actorType: "SYSTEM",
          description: "System logs audit trail for compliance",
          routeType: "Sequential",
          id: "GO1.PW9",
          goId: "GO1",
          loId: "GO1.LO9",
          routes: ["GO1.LO8"]),
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Process Flow Tree'),
        backgroundColor: Colors.blue.shade100,
      ),
      body: ProcessFlowWidget(processList: processList),
    );
  }
}

// Custom painter for solid vertical lines (like in reference image)
class DottedLinePainter extends CustomPainter {
  final Color color;
  final double dashLength;
  final double dashGap;

  DottedLinePainter({
    required this.color,
    this.dashLength = 5.0,
    this.dashGap = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw solid line instead of dotted for better visibility
    canvas.drawLine(
      Offset(0, 0),
      Offset(0, size.height),
      paint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// Custom painter for solid horizontal lines (like in reference image)
class HorizontalDottedLinePainter extends CustomPainter {
  final Color color;
  final double dashLength;
  final double dashGap;

  HorizontalDottedLinePainter({
    required this.color,
    this.dashLength = 3.0,
    this.dashGap = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw solid line instead of dotted for better visibility
    canvas.drawLine(
      Offset(0, 0),
      Offset(size.width, 0),
      paint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

Widget getCircle() {
  return Container(
    margin: EdgeInsets.only(left: 4, right: 8),
    width: 6,
    height: 6,
    decoration: BoxDecoration(
      shape: BoxShape.circle,
      border: Border.all(color: Color(0xff797676), width: 0.5),
      color: Colors.white,
    ),
  );
}

// Custom painter for alternate node tree lines
class AlternateTreeLinePainter extends CustomPainter {
  final bool isLast;
  final bool isFirst;
  final Color color;

  AlternateTreeLinePainter({
    required this.isLast,
    required this.isFirst,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    double centerX = 12.0; // Center of the connection area
    double centerY = size.height / 2;

    if (isFirst) {
      // For the first alternate item:
      // 1. Vertical line from parent (top) down to center
      _drawDottedLine(
        canvas,
        paint,
        Offset(centerX, 0),
        Offset(centerX, centerY),
      );

      // 2. If not the only item, continue vertical line down
      if (!isLast) {
        _drawDottedLine(
          canvas,
          paint,
          Offset(centerX, centerY),
          Offset(centerX, size.height),
        );
      }
    } else {
      // For middle and last items:
      // 1. Vertical line from top to center
      _drawDottedLine(
        canvas,
        paint,
        Offset(centerX, 0),
        Offset(centerX, centerY),
      );

      // 2. If not the last item, continue vertical line down
      if (!isLast) {
        _drawDottedLine(
          canvas,
          paint,
          Offset(centerX, centerY),
          Offset(centerX, size.height),
        );
      }
    }

    // 3. Horizontal line from vertical line to the circle position
    _drawDottedLine(
      canvas,
      paint,
      Offset(centerX, centerY),
      Offset(size.width, centerY),
    );
  }

  void _drawDottedLine(Canvas canvas, Paint paint, Offset start, Offset end) {
    const double dashLength = 2.5;
    const double dashGap = 1.5;

    double distance = (end - start).distance;
    if (distance == 0) return;

    double dashAndGap = dashLength + dashGap;
    int dashCount = (distance / dashAndGap).floor();

    Offset direction = (end - start) / distance;

    for (int i = 0; i < dashCount; i++) {
      Offset dashStart = start + direction * (i * dashAndGap);
      Offset dashEnd = dashStart + direction * dashLength;
      canvas.drawLine(dashStart, dashEnd, paint);
    }

    // Draw remaining part if any
    double remaining = distance % dashAndGap;
    if (remaining > 0 && remaining >= dashLength) {
      Offset dashStart = start + direction * (dashCount * dashAndGap);
      Offset dashEnd = dashStart + direction * dashLength.clamp(0, remaining);
      canvas.drawLine(dashStart, dashEnd, paint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// void main(){
//   runApp(
//     MaterialApp(
//     home: ProcessFlowExample())
//     );
// }
