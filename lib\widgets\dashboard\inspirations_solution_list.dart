import 'package:flutter/material.dart';

final List<Map<String, dynamic>> cardData = [
  {
    'title': 'Procurement Management',
    'image': 'assets/images/placeholder1.png',
  },
  {'title': 'My Modules & My team', 'image': 'assets/images/placeholder2.png'},
  {
    'title': 'Organisation Guides And Business Modules',
    'image': 'assets/images/placeholder3.png',
  },
  {'title': 'Leave\nmanagement', 'image': 'assets/images/placeholder4.png'},
  {'title': 'Apply Leave', 'image': 'assets/images/placeholder5.png'},
  {'title': 'Leave approval', 'image': 'assets/images/placeholder5.png'},
  {'title': 'Leave Balance', 'image': 'assets/images/placeholder5.png'},
  {'title': 'Approve/Reject PO', 'image': 'assets/images/placeholder5.png'},
];

class IsList extends StatefulWidget {
  const IsList({super.key});

  @override
  State<IsList> createState() => _IsListState();
}

class _IsListState extends State<IsList> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(children: [
        Padding(
          padding: EdgeInsets.only(
            bottom: 40,
          ), // Add bottom padding to prevent overflow
          child: SizedBox(
            width: 1100,
            child: GridView.count(
              crossAxisCount: 8,
              childAspectRatio: 0.8, // Increased to reduce height
              crossAxisSpacing: 12,
              mainAxisSpacing: 12, // Reduced spacing
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              children: cardData.map((item) {
                return Column(
                  children: [
                    Expanded(
                      flex: 3,
                      child: Card(
                        elevation: 2,
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(
                            color: const Color.fromARGB(
                              255,
                              236,
                              236,
                              236,
                            ),
                            width: 1,
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(10.0),
                          child: Center(
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.asset(
                                item['image'],
                                width: double.infinity,
                                height: 100,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Align(
                        alignment:
                            Alignment.topLeft, // Ensures text is left-aligned
                        child: Text(
                          item['title'],
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                          textAlign: TextAlign.left,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          ),
        ),
        SizedBox(height: 0),
      ]),
    );
  }
}
