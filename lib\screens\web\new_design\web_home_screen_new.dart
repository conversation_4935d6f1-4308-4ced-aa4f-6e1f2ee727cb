import 'dart:convert';
import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_svg/svg.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/models/agent_data.dart';
import 'package:nsl/models/chat_message.dart';
import 'package:nsl/models/conversation_response.dart';
import 'package:nsl/models/entities_data.dart';
import 'package:nsl/models/entities_data.dart' as attribute;
import 'package:nsl/models/role_info.dart';

import 'package:nsl/providers/auth_provider.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/workflow_data_provider.dart';

// import 'package:nsl/screens/web/new_design/my_library/book_detail_page.dart';
import 'package:nsl/screens/web/new_design/nsl_java_converter.dart';
import 'package:nsl/screens/web/new_design/web_book_detail_page.dart';
import 'package:nsl/screens/web/new_design/web_book_solution_page.dart';
import 'package:nsl/screens/web/new_design/web_my_library_screen.dart';
import 'package:nsl/screens/web/new_design/web_object_screen.dart';
import 'package:nsl/screens/web/new_design/web_solutions_screen.dart';
import 'package:nsl/screens/web/new_design/workflow_tree_builder.dart';
// import 'package:nsl/screens/web/new_design/web_my_library_screen.dart';
// import 'package:nsl/screens/web/new_design/web_solutions_screen.dart';

import 'package:nsl/screens/web/new_design/widgets/sidebar.dart';
import 'package:nsl/screens/web_transaction/web_collection_widgets.dart';
import 'package:nsl/screens/web_transaction/web_home_transaction.dart';
import 'package:nsl/screens/web_transaction/web_transaction_collection.dart';
import 'package:nsl/screens/web_transaction/web_transaction_solution.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:nsl/widgets/build_role_card.dart';
import 'package:nsl/widgets/custom_expansion_tile.dart';
import 'package:nsl/widgets/dashboard/inspirations_solution_list.dart';
import 'package:nsl/widgets/workflow_side_panel.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/services/file_upload_ocr_service.dart';

import 'package:nsl/widgets/custom_checkbox.dart';
import 'package:nsl/widgets/message_action_buttons.dart';
import 'package:nsl/widgets/ocr_text_side_panel.dart';
import 'package:nsl/widgets/speech_recognition_widget.dart';
import 'package:nsl/widgets/user_profile_card.dart';
import 'package:nsl/widgets/multimedia_widgets.dart';
import 'package:nsl/widgets/file_upload_preview.dart';
import 'package:nsl/widgets/file_upload_response_preview.dart';
import 'package:nsl/screens/web/new_design/widgets/web_audio_widget.dart';
import 'package:nsl/screens/web/new_design/widgets/audio_wave_visualizer_widget.dart';
import 'package:provider/provider.dart';

// Custom ExpansionTile that separates title tap from expansion toggle
class CustomExpansionTile extends StatefulWidget {
  final Widget title;
  final List<Widget> children;
  final bool initiallyExpanded;
  final Function(bool) onExpansionChanged;
  final VoidCallback onTitleTap;
  final Color backgroundColor;

  const CustomExpansionTile({
    super.key,
    required this.title,
    required this.children,
    this.initiallyExpanded = false,
    required this.onExpansionChanged,
    required this.onTitleTap,
    this.backgroundColor = Colors.white,
  });

  @override
  State<CustomExpansionTile> createState() => CustomExpansionTileState();
}

class CustomExpansionTileState extends State<CustomExpansionTile>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _heightFactor;
  late Animation<double> _iconTurn;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 200),
      vsync: this,
    );
    _heightFactor = _controller.drive(CurveTween(curve: Curves.easeIn));
    _iconTurn = _controller.drive(Tween<double>(begin: 0.0, end: 0.5)
        .chain(CurveTween(curve: Curves.easeIn)));
    _isExpanded = widget.initiallyExpanded;
    if (_isExpanded) {
      _controller.value = 1.0;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
      widget.onExpansionChanged(_isExpanded);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Title row with combined tap handlers
        !_isExpanded
            ? Container(
                alignment: Alignment.centerLeft,
                decoration: BoxDecoration(
                  color: Color(0xffF7F9FB),
                  // color: Colors.grey.shade50,
                  border: Border(
                    bottom: BorderSide(
                      color: _isExpanded
                          ? Colors.grey.shade200
                          : Colors.transparent,
                      width: 1.0,
                    ),
                  ),
                ),
                child: InkWell(
                  onTap: () {
                    // Call both onTitleTap and toggle expansion when title is tapped
                    widget.onTitleTap();
                    _toggleExpansion();
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    mainAxisSize:
                        _isExpanded ? MainAxisSize.max : MainAxisSize.min,
                    children: [
                      widget.title,
                      // Arrow icon that toggles expansion
                      Container(
                        color: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 7.0),
                        child: HoverArrowIcon(
                          onTap: _toggleExpansion,
                          iconTurn: _iconTurn,
                        ),
                      )
                    ],
                  ),
                ),
              )
            : SizedBox(),
        // Expandable content
        Transform.translate(
          offset: Offset(0, -2),
          child: AnimatedBuilder(
            animation: _controller.view,
            builder: (context, child) {
              return ClipRect(
                child: Align(
                  heightFactor: _heightFactor.value,
                  alignment: Alignment.topCenter, // Center align for full width
                  child: child,
                ),
              );
            },
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                // border: Border(
                //   top: BorderSide(
                //     color: Colors.grey.shade200,
                //     width: 1,
                //   ),
                // ),
              ),
              // Width is full when expanded
              width: _isExpanded ? double.infinity : null,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  InkWell(
                    onTap: () {
                      // Call both onTitleTap and toggle expansion when title is tapped
                      widget.onTitleTap();
                      _toggleExpansion();
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize:
                          _isExpanded ? MainAxisSize.max : MainAxisSize.min,
                      children: [
                        widget.title,
                        // Arrow icon that toggles expansion
                        Container(
                          color: Colors.white,
                          padding: EdgeInsets.symmetric(vertical: 9.0),
                          child: HoverArrowIcon(
                            onTap: _toggleExpansion,
                            iconTurn: _iconTurn,
                          ),
                        )
                      ],
                    ),
                  ),
                  ...widget.children.map((widget) => widget)
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class WebHomeScreenNew extends StatefulWidget {
  const WebHomeScreenNew({super.key});

  @override
  State<WebHomeScreenNew> createState() => _WebHomeScreenNewState();
}

// RoleInfo model is now imported from lib/models/role_info.dart

// EntityAttribute and EntityInfo are now replaced by Entity and Attribute from EntitiesData model

// Custom dropdown widget that looks exactly like the image
class CustomEntityVersionDropdown extends StatefulWidget {
  final List<String> versions;
  final String selectedVersion;
  final Function(String) onVersionSelected;

  const CustomEntityVersionDropdown({
    super.key,
    required this.versions,
    required this.selectedVersion,
    required this.onVersionSelected,
  });

  @override
  State<CustomEntityVersionDropdown> createState() =>
      _CustomEntityVersionDropdownState();
}

class _CustomEntityVersionDropdownState
    extends State<CustomEntityVersionDropdown> {
  bool isDropdownOpen = false;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  @override
  void dispose() {
    _removeOverlay();
    super.dispose();
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _toggleDropdown() {
    if (isDropdownOpen) {
      _removeOverlay();
    } else {
      _showDropdown();
    }
    setState(() {
      isDropdownOpen = !isDropdownOpen;
    });
  }

  void _showDropdown() {
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    var offset = renderBox.localToGlobal(Offset.zero);

    return OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx,
        top: 0, // Position at the top of the screen
        width: 200,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0.0, 0.0), // No offset needed
          child: Material(
            elevation: 4.0,
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                // No border
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: widget.versions.map((version) {
                  return InkWell(
                    onTap: () {
                      widget.onVersionSelected(version);
                      _toggleDropdown();
                    },
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      child: Text(
                        version,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          // No border
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
        child: InkWell(
          onTap: _toggleDropdown,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.folder_outlined,
                size: 20,
                color: Colors.black,
              ),
              Icon(
                Icons.keyboard_arrow_down,
                size: 20,
                color: Colors.black,
              ),
              SizedBox(width: 8),
              Text(
                widget.selectedVersion,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[700],
                  fontFamily: 'TiemposText',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Using EntityGroup directly from EntitiesData model

class _WebHomeScreenNewState extends State<WebHomeScreenNew> {
  final TextEditingController chatController = TextEditingController();
  // messages is now managed by the WebHomeProvider
  // currentScreenIndex is now managed by the WebHomeProvider
  bool isLoading = false;
  bool isLoadingWorkflows = false; // Loading state for workflows
  bool showSidePanel = false;
  bool hasTextInChatField = false; // Track if there's text in the chat field

  // Helper method to access the provider's messages list
  List<ChatMessage> get messages =>
      Provider.of<WebHomeProvider>(context, listen: false).messages;

  // OCR side panel state
  bool showOcrPanel = false;
  String ocrText = '';
  String ocrFileName = '';

  // File upload state for the new UI
  bool isFileUploaded = false;
  String uploadedFileName = '';
  String uploadedFileText = '';

  // Multimedia service
  final MultimediaService _multimediaService = MultimediaService();

  // Process API response with translation if needed
  Future<void> _processApiResponseWithTranslation(
      Map<String, dynamic> response, String messageType) async {
    final provider = Provider.of<WebHomeProvider>(context, listen: false);

    if (response['success']) {
      final String answer = response['data']['answer'] ?? 'No answer provided';

      // Create a message with reasoning data
      final ChatMessage message = ChatMessage(
        content: answer,
        isUser: false,
      );

      // Add reasoning data if available
      if (response['reasoning'] != null) {
        message.reasoningData = List<String>.from(response['reasoning']);
      }

      // Check if translation is needed
      if (_multimediaService.needsTranslation(context) &&
          answer != 'No answer provided') {
        // Show loading state for translation
        setState(() {
          isLoading = false;
          provider.addMessage(ChatMessage(
            content: 'Translating response...',
            isUser: false,
          ));
        });

        try {
          // Get the target language
          final targetLanguage =
              _multimediaService.getCurrentLanguageCode(context);
          Logger.info('Translating answer to $targetLanguage');

          // Call the translation service through the multimedia service
          final translationResult =
              await _multimediaService.translateText(answer, targetLanguage);

          if (translationResult['success']) {
            // Get the translated text from the result
            final String translatedText = translationResult['translatedText'];

            // Create a new message with the translated content
            final ChatMessage translatedMessage = ChatMessage(
              content: translatedText,
              isUser: false,
            );

            // Copy reasoning data if available
            if (message.reasoningData != null) {
              translatedMessage.reasoningData = message.reasoningData;
            }

            // Copy file data if available
            if (message.fileData != null) {
              translatedMessage.fileData = message.fileData;
            }

            // Update UI with translated message
            setState(() {
              isLoading = false;

              // Remove the "Translating..." message
              if (messages.isNotEmpty &&
                  messages.last.content.startsWith('Translating response')) {
                provider.messages.removeLast();
              }

              // Add only the translated message (no original text)
              provider.addMessage(translatedMessage);
            });
          } else {
            // Translation failed, use the original message
            Logger.error('Translation failed: ${translationResult['message']}');
            setState(() {
              isLoading = false;

              // Remove the "Translating..." message
              if (messages.isNotEmpty &&
                  messages.last.content.startsWith('Translating response')) {
                provider.messages.removeLast();
              }

              // Add the original message
              provider.addMessage(message);
            });
          }
        } catch (e) {
          // Error during translation, use the original message
          Logger.error('Error during translation: $e');
          setState(() {
            isLoading = false;

            // Remove the "Translating..." message
            if (messages.isNotEmpty &&
                messages.last.content.startsWith('Translating response')) {
              provider.messages.removeLast();
            }

            // Add the original message
            provider.addMessage(message);
          });
        }
      } else {
        // No translation needed, just add the message
        setState(() {
          isLoading = false;

          // Check if the last user message had file data
          Map<String, dynamic>? fileData;
          if (messages.isNotEmpty &&
              messages.last.isUser &&
              messages.last.fileData != null) {
            fileData = messages.last.fileData;
            Logger.info(
                'Including file data in response message: ${fileData?['fileName']}');

            // Add file data to the message
            message.fileData = fileData;
          }

          provider.addMessage(message);
        });
      }
    } else {
      // Add error message
      setState(() {
        isLoading = false;
        provider.addMessage(ChatMessage(
          content: 'Error: ${response['message']}',
          isUser: false,
        ));
      });
    }
  }

  // ScrollController for chat messages
  final ScrollController _chatScrollController = ScrollController();

  // Audio player
  final AudioPlayer audioPlayer = AudioPlayer();

  // Audio playback state
  String? currentPlayingMessageId;
  bool isPlaying = false;
  bool isPaused = false;
  Duration currentPosition = Duration.zero;
  Duration totalDuration = Duration.zero;
  String? currentAudioFilePath;

  // Recording state
  bool isRecording = false;

  // Speech recognition state
  String _recognizedText = "";

  // Add file upload and OCR messages to chat
  void addFileUploadAndOcrMessages(String fileName, String extractedText) {
    setState(() {
      ocrFileName = fileName;
      ocrText = extractedText;
      showOcrPanel = true;
      chatController.text = extractedText;
    });

    // Scroll to bottom after the UI updates
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  // Role-related variables
  RoleInfo? selectedRole;
  List<RoleInfo> roles = [];
  Map<String, dynamic> roleSystemInfo = {};
  bool isLoadingRoles = true;
// Global EntitiesData variable
  EntitiesData globalEntitiesData = EntitiesData();
  // Global AgentData variable
  AgentData globalAgentData = AgentData();
  // Entity-related variables
  Entity? selectedEntity;

  // Track selected section index for section abbreviations
  int selectedSectionIndex = 0;

  bool isLoadingEntities = true;

  // We'll use the provider's session ID instead of storing it locally

  List localData = ["role", "agent", "entity", "workflow", "entities"];

  // Circuit board tab selection
  String selectedCircuitTab = 'Synthetic';

  // Quick message selection
  String? get selectedQuickMessage =>
      Provider.of<WebHomeProvider>(context, listen: false).selectedQuickMessage;
  // = "Solution";

  // Workflow-related variables
  List<Map<String, dynamic>> workflows = [];
  Map<String, dynamic> workflowSystemInfo = {};
  Map<String, dynamic>? selectedWorkflow;

  // Chat history expansion state is now managed by the WebHomeProvider

  // NSL Thinking expansion state - maps message index to expansion state
  Map<int, bool> nslThinkingExpanded = {};

  // Side panel resizing
  double sidePanelWidth = 480.0; // Default width

  // ScrollController for workflow tree horizontal scrolling
  final ScrollController _workflowTreeScrollController = ScrollController();
  double minSidePanelWidth = 250.0; // Minimum width
  double maxSidePanelWidth = 600.0; // Maximum width
  bool isResizing = false;

  // Profile tooltip overlay
  OverlayEntry? _profileTooltipOverlay;

  // Flag to track which tooltip is currently active
  String? _activeTooltipType;

  @override
  void initState() {
    super.initState();
    // Initialize with no selected role or entity
    selectedRole = null;
    selectedEntity = null;
    selectedWorkflow = null;

    // Add listener to chat controller to detect text changes
    chatController.addListener(() {
      final hasText = chatController.text.trim().isNotEmpty;
      if (hasText != hasTextInChatField) {
        setState(() {
          hasTextInChatField = hasText;
        });
      }
    });

    // Initialize multimedia service
    _multimediaService.initialize();

    // Set up multimedia service callbacks
    _multimediaService.onStateChanged = () {
      if (mounted) {
        setState(() {
          // Update UI state based on multimedia service state
          isPlaying = _multimediaService.isPlaying;
          isPaused = _multimediaService.isPaused;
          isRecording = _multimediaService.isRecording;
          currentPlayingMessageId = _multimediaService.currentPlayingMessageId;
          currentPosition = _multimediaService.currentPosition;
          totalDuration = _multimediaService.totalDuration;
        });
      }
    };

    // Set up additional multimedia service listeners for text recognition
    _setupMultimediaServiceListeners();

    _multimediaService.onFileProcessed = (fileName, extractedText) {
      if (mounted) {
        // Add file upload and OCR messages to chat
        setState(() {
          ocrFileName = fileName;
          ocrText = extractedText;
          showOcrPanel = true;
          chatController.text = extractedText;
        });

        // Scroll to bottom after the UI updates
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      }
    };

    // Load data from API
    _loadRolesData();
    _loadEntitiesData();
    // Note: Workflow data will be loaded directly from API responses
    // We don't preload workflow data from JSON anymore

    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        // Load chat history
        _loadChatHistory();

        // Set up listener for WebHomeProvider's currentScreenIndex changes
        _setupWebHomeProviderListener();
      },
    );
  }

  // Set up listener for WebHomeProvider's currentScreenIndex changes
  void _setupWebHomeProviderListener() {
    if (!mounted) return;

    try {
      final webHomeProvider =
          Provider.of<WebHomeProvider>(context, listen: false);

      // Add listener for changes to currentScreenIndex
      webHomeProvider.addListener(() {
        if (mounted) {
          // When currentScreenIndex changes, log the change
          final currentIndex = webHomeProvider.currentScreenIndex;
          Logger.info(
              'WebHomeProvider currentScreenIndex changed to: $currentIndex');
        }
      });
    } catch (e) {
      Logger.error('Error setting up WebHomeProvider listener: $e');
    }
  }

  // Set up multimedia service listeners for text recognition
  void _setupMultimediaServiceListeners() {
    if (!mounted) return;

    try {
      // Set up a listener for recognized text
      _multimediaService.onTextRecognized = (text) {
        if (mounted) {
          setState(() {
            // Update recognized text in the chat field
            _recognizedText = text;
            chatController.text = text;

            // Log the recognized text
            Logger.info('Text recognized from audio: $text');
          });
        }
      };
    } catch (e) {
      Logger.error('Error setting up multimedia service listeners: $e');
    }
  }

  @override
  void dispose() {
    // Clean up the controllers when the widget is disposed
    chatController.dispose();
    _chatScrollController.dispose();
    _hideAllTooltips(); // Make sure to remove any active tooltips

    // Dispose audio player
    audioPlayer.dispose();

    // Dispose recording service
    _multimediaService.dispose();

    // Remove WebHomeProvider listener
    try {
      final webHomeProvider =
          Provider.of<WebHomeProvider>(context, listen: false);
      webHomeProvider.removeListener(() {});
    } catch (e) {
      Logger.error('Error removing WebHomeProvider listener: $e');
    }

    super.dispose();
  }

  // Hide profile tooltip
  void _hideProfileTooltip() {
    _profileTooltipOverlay?.remove();
    _profileTooltipOverlay = null;
    if (_activeTooltipType == 'profile') {
      _activeTooltipType = null;
    }
    // Update last click time to prevent tooltip from showing again immediately
    _lastClickTime = DateTime.now();
  }

  // Workflow tooltip overlay
  OverlayEntry? _workflowTooltipOverlay;

  // Track the last click time to prevent tooltip from showing during clicks
  DateTime _lastClickTime = DateTime.now();

  // Show workflow profile tooltip positioned near the workflow title
  void _showWorkflowProfileTooltip(
      GlobalKey titleKey, Map<String, dynamic> workflow) {
    // Don't show if another tooltip is active
    if (_activeTooltipType != null && _activeTooltipType != 'workflow') {
      return;
    }

    // Don't show tooltip if this workflow is already selected and side panel is visible
    if (showSidePanel &&
        selectedWorkflow != null &&
        selectedWorkflow!['id'] == workflow['id']) {
      return;
    }

    // Don't show tooltip if the user is clicking (this is a workaround for the hover/click issue)
    if (DateTime.now().difference(_lastClickTime).inMilliseconds < 300) {
      return;
    }

    // Hide any existing tooltips first
    _hideAllTooltips();

    // Get the position of the workflow title
    final RenderBox? renderBox =
        titleKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final position = renderBox.localToGlobal(Offset.zero);

    // Create the overlay entry
    _workflowTooltipOverlay = OverlayEntry(
      builder: (context) => Positioned(
        left: position.dx,
        // Position just below the workflow title
        top: position.dy + 20,
        child: Material(
          color: Colors.transparent,
          child: WorkflowProfileCard(
            id: workflow['id'] ?? '',
            version: workflow['version'] ?? '',
            createdBy: workflow['createdBy'] ?? '',
            createdDate: workflow['createdDate'] ?? '',
            modifiedBy: workflow['modifiedBy'] ?? '',
            modifiedDate: workflow['modifiedDate'] ?? '',
            title: workflow['mainTitle'] ?? workflow['title'] ?? '',
          ),
        ),
      ),
    );

    // Add the overlay to the screen
    Overlay.of(context).insert(_workflowTooltipOverlay!);
    _activeTooltipType = 'workflow';
  }

  // Hide workflow profile tooltip
  void _hideWorkflowProfileTooltip() {
    _workflowTooltipOverlay?.remove();
    _workflowTooltipOverlay = null;
    if (_activeTooltipType == 'workflow') {
      _activeTooltipType = null;
    }
    // Update last click time to prevent tooltip from showing again immediately
    _lastClickTime = DateTime.now();
  }

  // Hide all tooltips
  void _hideAllTooltips() {
    _hideProfileTooltip();
    _hideWorkflowProfileTooltip();
    _activeTooltipType = null;
    // Update last click time to prevent tooltip from showing again immediately
    _lastClickTime = DateTime.now();
  }

  // Show profile card tooltip positioned above the entity title
  void _showProfileTooltip(GlobalKey titleKey, Entity entity) {
    // Don't show if another tooltip is active
    if (_activeTooltipType != null && _activeTooltipType != 'profile') {
      return;
    }

    // Don't show tooltip if this entity is already selected and side panel is visible
    if (showSidePanel &&
        selectedEntity != null &&
        selectedEntity!.id == entity.id) {
      return;
    }

    // Don't show tooltip if the user is clicking (this is a workaround for the hover/click issue)
    if (DateTime.now().difference(_lastClickTime).inMilliseconds < 300) {
      return;
    }

    // Hide any existing tooltips first
    _hideAllTooltips();

    // Get the position of the entity title
    final RenderBox? renderBox =
        titleKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final position = renderBox.localToGlobal(Offset.zero);

    // Create the overlay entry
    _profileTooltipOverlay = OverlayEntry(
      builder: (context) => Positioned(
        left: position.dx,
        // Position above the entity title with a small offset
        top: position.dy + 20, // Position just above the entity title
        child: Material(
          color: Colors.transparent,
          child: EntityProfileCard(
            id: entity.id ?? '',
            version: entity.version ?? '',
            createdBy: entity.createdBy ?? '',
            createdDate: _parseDate(entity.createdDate),
            modifiedBy: entity.modifiedBy ?? '',
            modifiedDate: _parseDate(entity.modifiedDate),
            title: entity.title ?? '',
          ),
        ),
      ),
    );

    // Add the overlay to the screen
    Overlay.of(context).insert(_profileTooltipOverlay!);
    _activeTooltipType = 'profile';
  }

  // Variable to track chat history loading state
  bool isLoadingChatHistory = false;

  // Scroll to the bottom of the chat
  void _scrollToBottom() {
    if (_chatScrollController.hasClients) {
      _chatScrollController.animateTo(
        _chatScrollController.position.maxScrollExtent,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  // Load chat history
  Future<void> _loadChatHistory() async {
    setState(() {
      isLoadingChatHistory = true;
    });

    final provider = Provider.of<WebHomeProvider>(context, listen: false);
    await provider.fetchChatHistory(); // Using AuthService.getUserId()

    setState(() {
      isLoadingChatHistory = false;
    });
  }

  // Reset conversation and clear messages
  void _resetConversation({bool preserveScreenIndex = true}) {
    // Clear the chat input field
    chatController.clear();

    // Use the provider to reset all UI state
    final provider = Provider.of<WebHomeProvider>(context, listen: false);
    provider.resetUIState(preserveScreenIndex: preserveScreenIndex);

    Logger.info(
        'Reset conversation with preserveScreenIndex: $preserveScreenIndex');
  }

  // Cancel the current request
  void _cancelRequest() async {
    // Get the provider before any async operations
    final provider = Provider.of<WebHomeProvider>(context, listen: false);

    // If recording, stop it
    if (isRecording) {
      await _multimediaService.stopSpeechRecognition();
      Logger.info('Speech recognition stopped by cancel request');
    }

    // Check if widget is still mounted after async operations
    if (!mounted) return;

    // Update state using the provider and local state
    provider.isLoading = false;

    // Add a message to indicate the request was cancelled
    provider.addMessage(ChatMessage(
      content: 'Request cancelled by user.',
      isUser: false,
    ));

    // Update local recording state
    setState(() {
      isLoading = false;
      isRecording = false;
    });

    // Scroll to bottom after the UI updates
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _scrollToBottom();
      }
    });
  }

  // Load roles data from JSON file
  Future<void> _loadRolesData() async {
    try {
      // If we already have roles data (from API), don't load from JSON
      if (roles.isNotEmpty) {
        Logger.info(
            '🔍 Using existing roles data from API (${roles.length} roles)');
        setState(() {
          isLoadingRoles = false;
        });
        return;
      }

      Logger.info(
          '📁 LOCAL JSON DATA SOURCE: Loading roles data from local JSON file');

      // Load the JSON file from assets
      final String jsonString =
          await rootBundle.loadString('assets/data/roles_data.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);

      // Parse roles data
      final List<dynamic> rolesData = jsonData['roles'];
      roles = rolesData.map((roleData) => RoleInfo.fromJson(roleData)).toList();

      // Parse system info
      roleSystemInfo = jsonData['systemInfo'];

      Logger.info(
          '📁 LOCAL JSON CONFIRMATION: Loaded ${roles.length} roles from JSON file');

      setState(() {
        isLoadingRoles = false;
      });
    } catch (e) {
      Logger.error('📁 LOCAL JSON ERROR: Error loading roles data: $e');
      // Handle error silently
      setState(() {
        isLoadingRoles = false;
        // Set default roles in case of error
        roles = [];
      });
    }
  }

  // Load entities data from JSON file
  Future<void> _loadEntitiesData() async {
    try {
      // Load the JSON file from assets
      final String jsonString =
          await rootBundle.loadString('assets/data/entities_data.json');

      // Parse the JSON data using the EntitiesData model and set the global variable
      globalEntitiesData = entitiesDataFromJson(jsonString);

      // Initialize selected versions in the globalEntitiesData
      globalEntitiesData.initializeSelectedVersions();

      setState(() {
        isLoadingEntities = false;
      });
    } catch (e) {
      // Handle error silently
      setState(() {
        isLoadingEntities = false;
        // Set default entity data in case of error
        globalEntitiesData = EntitiesData();
      });
    }
  }

  // Extract entities data from API response
  EntitiesData _extractEntitiesDataFromResponse(ResultEntity resultEntity) {
    try {
      // Create a new EntitiesData object
      EntitiesData entitiesData = EntitiesData();

      // Create entity groups list
      List<EntityGroup> entityGroups = [];

      if (resultEntity.versions != null && resultEntity.versions!.isNotEmpty) {
        // Process each version
        for (var version in resultEntity.versions!) {
          // Create a new entity group
          EntityGroup entityGroup = EntityGroup(
            id: "group_${version.versionId ?? DateTime.now().millisecondsSinceEpoch.toString()}",
            title: version.data?.firstOrNull?.data?.firstOrNull?.entities
                    ?.firstOrNull?.title ??
                "Entity Group",
            documentId: version.versionId,
            coreCount: "${version.data?.length ?? 0} Core",
            enitiyVersions: version.summary?.versions
                    ?.map((v) => v.versionId ?? "")
                    .toList() ??
                [],
            checked: false,
            entities: [],
          );

          // Process data in the version
          if (version.data != null) {
            List<Entity> entities = [];

            for (var datum in version.data!) {
              if (datum.data != null) {
                for (var datumData in datum.data!) {
                  if (datumData.entities != null) {
                    for (var entityElement in datumData.entities!) {
                      // Convert EntityElement to Entity
                      Entity entity = Entity(
                        id: entityElement.id,
                        title: entityElement.title,
                        description: entityElement.description,
                        version: entityElement.version,
                        expanded: entityElement.expanded,
                        checked: false,
                        attributeString: entityElement.attributeString,
                        createdBy: entityElement.createdBy,
                        createdDate: entityElement.createdDate != null
                            ? _parseDate(entityElement.createdDate!)
                            : null,
                        modifiedBy: entityElement.modifiedBy,
                        modifiedDate: entityElement.modifiedDate,
                        attributes: entityElement.attributes
                            ?.map((attr) => Attribute(
                                  name: attr.name,
                                  type: attr.type,
                                  required: attr.required,
                                  isPk: attr.isPk,
                                  description: attr.description,
                                  isFk: attr.isFk,
                                ))
                            .toList(),
                        businessRules: [],
                        relationType: entityElement.relationType,
                        parentEntity: entityElement.parentEntity,
                      );

                      entities.add(entity);
                    }
                  }
                }
              }
            }

            // Add entities to the group
            entityGroup = entityGroup.copyWith(entities: entities);
          }

          // Add the entity group to the list
          entityGroups.add(entityGroup);
        }
      }

      // Create system info
      SystemInfo systemInfo = SystemInfo(
        enitiyVersions:
            resultEntity.versions?.map((v) => v.versionId ?? "").toList() ?? [],
        entityName:
            resultEntity.versions?.firstOrNull?.summary?.title ?? "Entity Data",
        entityCount: resultEntity.versions
                ?.fold<int>(0, (sum, v) => sum + (v.data?.length ?? 0)) ??
            0,
        coreEntities: resultEntity.versions?.length ?? 0,
        headerText: "There are total 5 Object, 4 Core and 1 Transaction Object",
        bulletPoints: resultEntity.versions
                ?.map((v) =>
                    "${v.versionName ?? v.versionId} has ${v.data?.length ?? 0} entities")
                .toList() ??
            [],
      );

      // Update the EntitiesData object
      entitiesData = entitiesData.copyWith(
        entityGroups: entityGroups,
        systemInfo: systemInfo,
      );

      // Initialize selected versions
      entitiesData.initializeSelectedVersions();

      return entitiesData;
    } catch (e) {
      Logger.error('Error extracting entities data from response: $e');
      // Return empty EntitiesData in case of error
      return EntitiesData();
    }
  }

  // This method has been moved to WorkflowDataProvider

  // Update workflow data from API response
  Future<void> _updateWorkflowsFromApiResponse(
      ConversationResponse response, WebHomeProvider webHomeProvider) async {
    try {
      // Extract workflow data directly from the response
      List<Map<String, dynamic>> extractedWorkflows = [];

      // Check if we have agent results with workflow data
      if (response.workflowResult?.agentResults != null) {
        for (var agent in response.workflowResult!.agentResults!) {
          if (agent.result?.workFlow?.workFlowDetails != null) {
            final workflowDetails = agent.result!.workFlow!.workFlowDetails!;

            // Create a workflow map from the API data
            Map<String, dynamic> workflow = {
              'id': workflowDetails.id ??
                  'workflow_${DateTime.now().millisecondsSinceEpoch}',
              'title': workflowDetails.title ?? 'Untitled Workflow',
              'description': workflowDetails.description ?? '',
              'version': workflowDetails.version ?? '1.0',
              'createdBy': workflowDetails.createdBy ?? '',
              'createdDate': workflowDetails.createdDate ?? '',
              'modifiedBy': workflowDetails.modifiedBy ?? '',
              'modifiedDate': workflowDetails.modifiedDate ?? '',
              'mainTitle': workflowDetails.mainTitle ??
                  workflowDetails.title ??
                  'Untitled Workflow',
              'tree': _convertApiTreeToJsonFormat(workflowDetails.tree ?? []),
              'workFlowDetails': workflowDetails,
            };

            extractedWorkflows.add(workflow);
          }
        }
      }

      if (extractedWorkflows.isNotEmpty) {
        // Update our local workflows list directly
        workflows = extractedWorkflows;

        // Log detailed information about the extracted workflows
        Logger.info(
            '🔍 API SUCCESS: Extracted ${extractedWorkflows.length} workflows from API response');
        if (extractedWorkflows.isNotEmpty) {
          Logger.info(
              '🔍 API WORKFLOW DETAILS: First workflow title: "${extractedWorkflows[0]['title']}"');
          Logger.info(
              '🔍 API WORKFLOW DETAILS: First workflow ID: "${extractedWorkflows[0]['id']}"');
          Logger.info(
              '🔍 API WORKFLOW DETAILS: Tree nodes count: ${(extractedWorkflows[0]['tree'] as List).length}');
        }

        // Update the provider with our extracted data
        try {
          final workflowDataProvider =
              Provider.of<WorkflowDataProvider>(context, listen: false);
          await Future.microtask(() {
            workflowDataProvider.updateDirectly(extractedWorkflows);
            Logger.info(
                '🔍 PROVIDER UPDATE: Successfully updated WorkflowDataProvider with API data');
          });
        } catch (providerError) {
          // Log the error but continue with direct data
          Logger.error('Provider error (non-fatal): $providerError');
          Logger.info(
              '🔍 DIRECT DATA: Continuing with direct workflow data from API');
        }

        Logger.info(
            '🔍 API COMPLETE: Successfully updated workflows from API response: ${extractedWorkflows.length} workflows');
      } else {
        // If no workflows were found, log it and throw an exception
        Logger.info('🔍 API EMPTY: No workflow data found in the response');
        throw Exception('No workflow data found in API response');
      }
    } catch (e) {
      // Log the error but don't add an error message to the chat
      // The calling code will handle showing appropriate messages
      Logger.error('Error in _updateWorkflowsFromApiResponse: $e');
      rethrow; // Rethrow so the calling code can handle it
    }
  }

  // Helper method to convert API tree format to JSON format
  List<Map<String, dynamic>> _convertApiTreeToJsonFormat(
      List<dynamic> apiTree) {
    List<Map<String, dynamic>> result = [];

    for (var node in apiTree) {
      Map<String, dynamic> treeNode = {
        'id': node.id ?? 'node_${DateTime.now().millisecondsSinceEpoch}',
        'text': node.text ?? '',
        'level': node.level ?? 0,
        'sequence': node.sequence ?? 0,
        'altText': node.altText,
        'isRejection': node.isRejection ?? false,
        'isApproval': node.isApproval ?? false,
        'isParallel': node.isParallel ?? false,
        'hasCheckmark': node.hasCheckmark,
        'hasX': node.hasX,
      };

      // Add children if they exist
      if (node.children != null && node.children!.isNotEmpty) {
        treeNode['children'] = _convertApiChildrenToJsonFormat(node.children!);
      }

      result.add(treeNode);
    }

    return result;
  }

  // Helper method to convert API children to JSON format
  List<Map<String, dynamic>> _convertApiChildrenToJsonFormat(
      List<dynamic> apiChildren) {
    List<Map<String, dynamic>> result = [];

    for (var child in apiChildren) {
      Map<String, dynamic> childNode = {
        'id': child.id ?? 'child_${DateTime.now().millisecondsSinceEpoch}',
        'text': child.text ?? '',
        'level': child.level ?? 0,
        'sequence': child.sequence ?? 0,
        'altText': child.altText,
        'isRejection': child.isRejection ?? false,
        'isApproval': child.isApproval ?? false,
        'isParallel': child.isParallel ?? false,
        'hasX': child.hasX,
      };

      // Add nested children if they exist
      if (child.children != null && child.children!.isNotEmpty) {
        childNode['children'] =
            _convertNestedChildrenToJsonFormat(child.children!);
      }

      result.add(childNode);
    }

    return result;
  }

  // Helper method to convert nested children to JSON format
  List<Map<String, dynamic>> _convertNestedChildrenToJsonFormat(
      List<dynamic> nestedChildren) {
    List<Map<String, dynamic>> result = [];

    for (var child in nestedChildren) {
      Map<String, dynamic> childNode = {
        'id': child.id ?? 'nested_${DateTime.now().millisecondsSinceEpoch}',
        'text': child.text ?? '',
        'level': child.level ?? 0,
        'sequence': child.sequence ?? 0,
        'altText': child.altText,
        'isRejection': child.isRejection ?? false,
        'isApproval': child.isApproval ?? false,
        'isParallel': child.isParallel ?? false,
        'hasCheckmark': child.hasCheckmark,
      };

      // Handle recursive children if they exist
      if (child.children != null && child.children!.isNotEmpty) {
        childNode['children'] =
            _convertNestedChildrenToJsonFormat(child.children!);
      }

      result.add(childNode);
    }

    return result;
  }

  // We no longer preload workflow data - it's loaded directly from API responses

  // Build workflows response using direct data (not from provider)
  Widget _buildWorkflowsResponseDirect() {
    // Show loading indicator if no workflows are available
    if (workflows.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Loading workflows data...',
              style: TextStyle(
                fontFamily: 'TiemposText',
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    // Get the first workflow from the list
    final workflow = workflows[0];

    // Get the tree data from the workflow
    final List<dynamic> treeData = workflow['tree'] ?? [];
    final String mainTitle = workflow['mainTitle'] ?? '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header row with workflow info
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Expanded(
            //   child: Consumer<AuthProvider>(
            //     builder: (context, authProvider, _) {
            //       // Get the username from the user profile
            //       final String firstName = authProvider.user?.username ?? '';

            //       // Get the localized greeting text
            //       final String greeting = AppLocalizations.of(context)
            //           .translate('build.workflowGreeting');

            //       // If we have a username, prepend it to the greeting
            //       final String displayText =
            //           firstName.isNotEmpty ? '$firstName, $greeting' : greeting;

            //       return Text(
            //         displayText,
            //         style: TextStyle(
            //           fontFamily: 'TiemposText',
            //           fontSize: 14,
            //           fontWeight: FontWeight.w500,
            //         ),
            //       );
            //     },
            //   ),
            // ),
            SvgPicture.asset('assets/images/eye.svg', height: 14, width: 18),
            SizedBox(width: 5),
            Text(
              'PREVIEW UI',
              style: TextStyle(
                fontSize: 9,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(width: 10),
            SvgPicture.asset('assets/images/icons/box.svg',
                height: 24, width: 24),
          ],
        ),

        SizedBox(height: AppSpacing.md),

        // Workflow visualization container
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(AppSpacing.xs),
            color: Colors.white,
          ),
          padding: EdgeInsets.all(AppSpacing.xxs),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Workflow header
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(AppSpacing.xs),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        "Workflow",
                        style: TextStyle(
                          fontFamily: 'TiemposText',
                          fontSize: 12.0,
                          color: Colors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Icon(Icons.folder_outlined,
                        size: 18, color: Colors.grey.shade600),
                    Icon(Icons.arrow_drop_down,
                        size: 18, color: Colors.grey.shade600),
                  ],
                ),
              ),

              SizedBox(height: AppSpacing.xs),

              // Workflow title
              Padding(
                padding: EdgeInsets.only(left: AppSpacing.xs),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Builder(builder: (context) {
                      final GlobalKey titleKey = GlobalKey(
                          debugLabel: 'workflowTitle_${workflow['id']}');

                      return MouseRegion(
                        cursor: SystemMouseCursors.click,
                        onEnter: (_) {
                          // Show workflow profile tooltip when hovering on title
                          _showWorkflowProfileTooltip(titleKey, workflow);
                        },
                        onExit: (_) {
                          // Hide profile tooltip when not hovering
                          _hideWorkflowProfileTooltip();
                        },
                        child: InkWell(
                          onTap: () {
                            // Update last click time to prevent tooltip from showing
                            _lastClickTime = DateTime.now();

                            // Hide any existing tooltip when clicking
                            _hideWorkflowProfileTooltip();

                            setState(() {
                              selectedRole = null;
                              selectedEntity = null;
                              selectedWorkflow = workflow;
                              showSidePanel = true;

                              // Scroll the workflow tree horizontally to show content that might be overflowing
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                if (_workflowTreeScrollController.hasClients) {
                                  _workflowTreeScrollController.animateTo(
                                    0, // Scroll to position 0 (leftmost content)
                                    duration: Duration(milliseconds: 500),
                                    curve: Curves.easeInOut,
                                  );
                                }
                              });
                            });
                          },
                          child: Text(
                            mainTitle,
                            key: titleKey,
                            style: TextStyle(
                              fontFamily: 'TiemposText',
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              ),

              SizedBox(height: AppSpacing.md),

              // Workflow tree visualization using WorkflowTreeBuilder
              Padding(
                padding: EdgeInsets.all(AppSpacing.xs),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  controller: _workflowTreeScrollController,
                  child: WorkflowTreeBuilder.buildWorkflowTree(treeData),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppSpacing.md),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    // Provider is now used directly in the Sidebar component
    return Scaffold(
      backgroundColor: Color(0xffF7F9FB),
      body: Row(
        children: [Sidebar(), Expanded(child: mainContent())],
      ),
    );
  }

  Widget mainContent() {
    final webHomeProvider = Provider.of<WebHomeProvider>(context);

    // Get the current screen index from the WebHomeProvider
    final currentScreenIndex = webHomeProvider.currentScreenIndex;

    // Log the current screen index
    Logger.info('Current screen index in mainContent: $currentScreenIndex');

    // If the current screen index is empty, set it to home
    if (currentScreenIndex.isEmpty) {
      // Use a post-frame callback to avoid setState during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          webHomeProvider.currentScreenIndex = ScreenConstants.home;
          Logger.info('Screen index was empty, set to home');
        }
      });
    }

    switch (currentScreenIndex) {
      case ScreenConstants.home:
        return homeBody();
      case ScreenConstants.create:
        return BookDetailPage();
      //  case ScreenConstants.myLibrary:
      //     return WebMyLibraryScreen();
      case ScreenConstants.nslJava:
        return NslJavaConverter();

      case ScreenConstants.webMyLibrary:
        return WebMyLibraryScreen();

      case ScreenConstants.webMySolution:
        return WebSolutionsScreen();

      case ScreenConstants.webBookSolution:
        return WebBookSolutionPage();

      case ScreenConstants.webMyObject:
        return WebObjectScreen();
      case ScreenConstants.myBusinessHome:
        return WebHomeTransaction();
      case ScreenConstants.myBusinessCollections:
        return WebTransactionCollection();
      case ScreenConstants.myBusinessSolutions:
        return WebTransactionSolution();
      case ScreenConstants.myBusinessRecords:
        return WebCollectionWidgets();
      default:
        return homeBody();
    }
  }

  Widget homeBody() {
    return Row(
      children: [
        // Main content area (resized when side panel is shown)
        Expanded(
          flex: 3, // Give it a larger flex value than the OCR panel
          child: Column(
            children: [
              // Different layouts based on whether chat has started
              if (messages.isEmpty)
                // Initial centered layout with welcome message and chat field
                Expanded(
                  child: Row(
                    children: [
                      chatHistory(),
                      Expanded(
                        child: Row(
                          children: [
                            Provider.of<WebHomeProvider>(context)
                                    .isChatHistoryExpanded
                                ? Container(
                                    width: 150,
                                  )
                                : Expanded(child: SizedBox()),
                            Expanded(
                              flex: 3,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Center(
                                    child: Consumer<AuthProvider>(
                                      builder: (context, authProvider, _) {
                                        // Get the username from the user profile
                                        final String firstName =
                                            authProvider.user?.username ?? '';

                                        // Get the localized greeting text
                                        final String greeting =
                                            AppLocalizations.of(context)
                                                .translate('home.greeting')
                                                .replaceAll(
                                                    '{name}',
                                                    firstName.isNotEmpty
                                                        ? firstName
                                                        : 'Mentor');

                                        return Text(
                                          greeting,
                                          style: TextStyle(
                                              fontSize: 34,
                                              fontFamily: 'TiemposText'),
                                        );
                                      },
                                    ),
                                  ),
                                  SizedBox(height: AppSpacing.md),
                                  // Chat field in the center
                                  chatField(context, height: 130),
                                  SizedBox(height: AppSpacing.xxs),
                                  // Quick message suggestions
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Wrap(
                                        alignment: WrapAlignment.start,
                                        spacing: AppSpacing.xs,
                                        runSpacing: AppSpacing.xs,
                                        children: [
                                          quickMessage(
                                              text: AppLocalizations.of(context)
                                                  .translate('home.nsl')),
                                          quickMessage(
                                              text: AppLocalizations.of(context)
                                                  .translate('home.solution')),
                                          quickMessage(
                                              text: AppLocalizations.of(context)
                                                  .translate('home.general')),
                                          quickMessage(
                                              text: AppLocalizations.of(context)
                                                  .translate('home.internet')),
                                        ],
                                      ),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      Padding(
                                        padding: EdgeInsets.only(
                                          top: 40,
                                        ), // Add bottom padding to prevent overflow
                                        child: SizedBox(
                                          width: 794,
                                          child: GridView.count(
                                            crossAxisCount: 6,
                                            childAspectRatio:1 / 3,
                                            crossAxisSpacing: 48,
                                            mainAxisSpacing:
                                                40, // Reduced spacing
                                            shrinkWrap: true,
                                            physics:
                                                NeverScrollableScrollPhysics(),
                                            children: cardData.map((item) {
                                              return Column(
                                                children: [
                                                  Expanded( 
                                                     child: Center(
                                                          // child: ClipRRect(
                                                          //   borderRadius:
                                                          //       BorderRadius
                                                          //           .circular(
                                                          //               8),
                                                          //   child: Image.asset(
                                                          //     item['image'],
                                                          //     width: double
                                                          //         .infinity,
                                                          //     height: 214,
                                                          //     fit: BoxFit.cover,
                                                          //   ),
                                                          // ),
                                                          child: ClipPath(
  clipper: CutCornerClipper(),
  child: Container(
    width: double.infinity,
    height: 220,
    decoration: BoxDecoration(
      color: Colors.lightBlue[100],
      border: Border.all(color: Colors.black26),
      borderRadius: BorderRadius.circular(6),
    ),
  ),
)
                                                        ),
                                                      
                                                  ),
                                                   SizedBox(height: 5),
                                                  Expanded(
                                                    child: Align(
                                                      alignment: Alignment
                                                          .topLeft, // Ensures text is left-aligned
                                                      child: Text(
                                                        item['title'],
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          color: Colors.black87,
                                                        ),
                                                        textAlign:
                                                            TextAlign.left,
                                                        maxLines: 3,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                      ),
                                                      
                                                    ),
                                                  ),
                                                ],
                                              );
                                            }).toList(),
                                          ),
                                        ),
                                      ),
                                    ],
                                  )
                                ],
                              ),
                              
                            ),
                            Provider.of<WebHomeProvider>(context)
                                    .isChatHistoryExpanded
                                ? Container(
                                    width: 150,
                                  )
                                : Expanded(child: SizedBox()),
                          ],
                        ),
                      ),
                    ],
                  ),
                )
              else
                Expanded(
                  child: Row(
                    children: [
                      chatHistory(),
                      showSidePanel
                          ? Container(width: AppSpacing.lg)
                          : Provider.of<WebHomeProvider>(context)
                                  .isChatHistoryExpanded
                              ? Container(
                                  width: 77,
                                )
                              : Expanded(child: SizedBox()),
                      Expanded(
                        flex: 7,
                        child: Column(
                          children: [
                            Expanded(
                              child: Builder(
                                builder: (context) {
                                  Logger.info(
                                      'Building ListView with ${messages.length} messages');
                                  return ListView.builder(
                                    key: ValueKey<int>(messages
                                        .length), // Add key to force rebuild
                                    controller: _chatScrollController,
                                    shrinkWrap: true,
                                    padding: EdgeInsets.all(AppSpacing.md),
                                    itemCount:
                                        messages.length + (isLoading ? 1 : 0),
                                    itemBuilder: (context, index) {
                                      if (index == messages.length) {
                                        // Show loading indicator
                                        return Container(
                                          margin: EdgeInsets.only(
                                              top: AppSpacing.md),
                                          padding:
                                              EdgeInsets.all(AppSpacing.md),
                                          // decoration: BoxDecoration(
                                          //   color: Colors.white,
                                          //   borderRadius:
                                          //       BorderRadius.circular(AppSpacing.md),
                                          //   border: Border.all(
                                          //       color: Color(0xffD0D0D0), width: 1),
                                          // ),
                                          child: Row(
                                            children: [
                                              SizedBox(
                                                width: 20,
                                                height: 20,
                                                child:
                                                    CircularProgressIndicator(
                                                  strokeWidth: 2,
                                                  valueColor:
                                                      AlwaysStoppedAnimation<
                                                          Color>(
                                                    Theme.of(context)
                                                        .colorScheme
                                                        .primary,
                                                  ),
                                                ),
                                              ),
                                              SizedBox(width: AppSpacing.sm),
                                              Text(
                                                'NSL Knowledge...',
                                                style: TextStyle(
                                                  fontFamily: 'TiemposText',
                                                  fontSize: 16,
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      }

                                      final message = messages[index];
                                      Logger.info(
                                          'Building message bubble for index $index: ${message.content}');
                                      return _buildMessageBubble(
                                          message, index);
                                    },
                                  );
                                },
                              ),
                            ),
                            Column(
                              children: [
                                // Clear chat button
                                // if (messages.isNotEmpty)
                                //   Align(
                                //     alignment: Alignment.centerRight,
                                //     child: Padding(
                                //       padding: const EdgeInsets.only(
                                //           right: 16.0, bottom: 8.0),
                                //       child: TextButton.icon(
                                //         onPressed: _resetConversation,
                                //         icon: Icon(Icons.delete_outline, size: 18),
                                //         label: Text('Clear Chat'),
                                //         style: TextButton.styleFrom(
                                //           foregroundColor: Colors.grey.shade700,
                                //         ),
                                //       ),
                                //     ),
                                //   ),
                                // Only show chat field if not showing side panel
                                (!showSidePanel)
                                    ? Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 8.0),
                                        child: chatField(context, height: 55),
                                      )
                                    : SizedBox(),
                              ],
                            ),
                          ],
                        ),
                      ),
                      showSidePanel
                          ? Container(width: AppSpacing.lg)
                          : Provider.of<WebHomeProvider>(context)
                                  .isChatHistoryExpanded
                              ? Container(width: 77)
                              : Expanded(child: SizedBox())
                    ],
                  ),
                ),
            ],
          ),
        ),
        // Toggle button for OCR panel when it has content
        if (ocrText.isNotEmpty)
          OcrPanelToggleButton(
            isShown: showOcrPanel,
            onToggle: toggleOcrPanel,
          ),
        // Side panel for role, entity, workflow details, or OCR results
        if (showSidePanel || showOcrPanel)
          _buildResizablePanel(
            width: sidePanelWidth,
            minWidth: minSidePanelWidth,
            maxWidth: maxSidePanelWidth,
            onResize: (newWidth) {
              setState(() {
                sidePanelWidth = newWidth;
              });
            },
            child: showOcrPanel
                ? OcrTextSidePanel(
                    ocrText: ocrText,
                    fileName: ocrFileName,
                  )
                : selectedRole != null
                    ? _buildRoleDetailsPanel(selectedRole!)
                    : selectedEntity != null
                        ? _buildEntityDetailsPanel(selectedEntity!)
                        : selectedWorkflow != null
                            ? WorkflowSidePanel(
                                workflowId: selectedWorkflow!['id'],
                                onClose: () {
                                  setState(() {
                                    showSidePanel = false;
                                    selectedWorkflow = null;
                                  });
                                },
                              )
                            : Container(),
          ),
      ],
    );
  }

  Widget chatHistory() {
    final provider = Provider.of<WebHomeProvider>(context);
    final chatHistoryList = provider.chatHistory;

    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      width: provider.isChatHistoryExpanded
          ? MediaQuery.of(context).size.width / 6
          : 0,
      decoration: BoxDecoration(
        color: Colors.white,
        // boxShadow: [
        //   BoxShadow(
        //     color: Colors.black.withValues(alpha: 0.13), // 0.05 * 255 ≈ 13
        //     blurRadius: 5,
        //     offset: Offset(0, 3),
        //   ),
        // ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: EdgeInsets.only(
                left: AppSpacing.xs,
                right: AppSpacing.xs,
                bottom: AppSpacing.xxs,
                top: AppSpacing.xs),
            decoration: BoxDecoration(
                border: Border(
                    bottom:
                        BorderSide(color: Colors.grey.shade300, width: 0.5))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                provider.isChatHistoryExpanded
                    ? Expanded(
                        child: Text(
                          "Chat History",
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 12,
                          ),
                        ),
                      )
                    : Container(),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Add clickable image that only shows when chat has been submitted
                    if (messages
                        .isNotEmpty) // Only show if there are chat messages
                      GestureDetector(
                        onTap: () {
                          // Redirect to homepage to add new chat
                          // Don't reset the current chat, just navigate to home

                          // Navigate to home screen to start a new chat
                          provider.isChatHistoryExpanded =
                              !provider.isChatHistoryExpanded;
                          provider.currentScreenIndex = ScreenConstants.home;
                          // Reset the conversation
                          _resetConversation();
                        },
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: SvgPicture.asset(
                            'assets/images/add_chat_history.svg',
                            height: 16,
                            width: 16,
                          ),
                        ),
                      ),
                    if (messages.isNotEmpty)
                      const SizedBox(
                          width: AppSpacing
                              .xs), // Small spacing between image and icon
                    IconButton(
                      icon: Icon(
                          provider.isChatHistoryExpanded
                              ? Icons.close
                              : Icons.chevron_right,
                          color: Colors.grey.shade600,
                          size: 18),
                      onPressed: () {
                        // Toggle chat history expanded state
                        provider.isChatHistoryExpanded =
                            !provider.isChatHistoryExpanded;
                        // Also refresh chat history when expanding
                        if (provider.isChatHistoryExpanded) {
                          _loadChatHistory();
                        }
                      },
                      tooltip: provider.isChatHistoryExpanded
                          ? 'Collapse'
                          : 'Expand',
                      iconSize: 20,
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // SizedBox(
          //   height: AppSpacing.xxs,
          // ),

          // Chat history list - only show when expanded
          if (provider.isChatHistoryExpanded)
            Expanded(
              child: isLoadingChatHistory
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          SizedBox(height: 16),
                          Text(
                            context.tr('home.loadingChatHistory'),
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    )
                  : chatHistoryList.isEmpty
                      ? Center(
                          child: Text(
                            context.tr('home.noChatHistory'),
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 14,
                            ),
                          ),
                        )
                      : Builder(
                          builder: (context) {
                            // Filter chat history items to only include those with conversation_id
                            final filteredChatHistory = chatHistoryList
                                .where(
                                    (chat) => chat['conversation_id'] != null)
                                .toList();

                            // Log the filtering results
                            // Logger.info(
                            //     'Chat history filtering: ${chatHistoryList.length} total items, ${filteredChatHistory.length} with conversation_id');
                            // for (var chat in chatHistoryList) {
                            //   Logger.info(
                            //       'Chat item: id=${chat['id']}, conversation_id=${chat['conversation_id']}, title=${chat['title']}');
                            // }

                            return filteredChatHistory.isEmpty
                                ? Center(
                                    child: Text(
                                      context.tr('home.noChatHistory'),
                                      style: TextStyle(
                                        color: Colors.grey.shade600,
                                        fontSize: 14,
                                      ),
                                    ),
                                  )
                                : ListView.builder(
                                    itemCount: filteredChatHistory.length,
                                    itemBuilder: (context, index) {
                                      final chat = filteredChatHistory[index];
                                      return _buildChatHistoryItem(chat);
                                    },
                                  );
                          },
                        ),
            ),
        ],
      ),
    );
  }

  // Build a chat history item
  Widget _buildChatHistoryItem(Map<String, dynamic> chat) {
    // Skip items without conversation_id (double check)
    if (chat['conversation_id'] == null) {
      Logger.info(
          'Skipping chat history item without conversation_id: ${chat['id']}');
      return SizedBox.shrink();
    }

    // Background color
    final backgroundColor = Colors.white;

    // Determine chat type based on title
    final title = chat['title']?.toString().toLowerCase() ?? '';
    String chatType = 'General'; // Default

    if (title.contains('general')) {
      chatType = 'General';
    } else if (title.contains('internet')) {
      chatType = 'Internet';
    } else if (title.contains('nsl')) {
      chatType = 'NSL';
    }

    // Create a stateful widget for hover effects
    return _ChatHistoryItemWidget(
      chat: chat,
      backgroundColor: backgroundColor,
      // chatTypeIcon: chatTypeIcon,
      isExpanded: Provider.of<WebHomeProvider>(context).isChatHistoryExpanded,
      onTap: () {
        // Load this conversation
        final provider = Provider.of<WebHomeProvider>(context, listen: false);
        final conversationId = chat['conversation_id']?.toString();

        if (conversationId != null) {
          // Show loading indicator
          setState(() {
            isLoading = true;
            provider.clearMessages();
          });

          // Load the conversation and fetch its history
          provider.loadConversation(conversationId, userId: 1).then((result) {
            if (result['success']) {
              // Set the appropriate quick message type based on the conversation title
              String messageType = chatType;

              // Debug the entire API response
              Logger.info('Full API response: $result');

              // Process the chat history
              final chatHistory = result['data']['messages'] as List<dynamic>?;

              // Debug the chat history
              Logger.info('Chat history response: $chatHistory');

              setState(() {
                isLoading = false;
                // selectedQuickMessage = messageType;
                provider.clearMessages(); // Clear existing messages first

                if (chatHistory != null && chatHistory.isNotEmpty) {
                  // Add messages from the chat history
                  for (var message in chatHistory) {
                    // Log the entire message object to understand its structure
                    Logger.info('Message object: $message');

                    final isUser = message['role'] == 'user';
                    // Check for 'message' field first, then fall back to 'content' field
                    final content =
                        message['message'] ?? message['content'] ?? '';

                    Logger.info(
                        'Adding message: ${isUser ? 'User' : 'NSL'} - $content');

                    provider.addMessage(ChatMessage(
                      content: content,
                      isUser: isUser,
                    ));
                  }

                  // Force UI to update
                  if (messages.isEmpty) {
                    provider.addMessage(ChatMessage(
                      content:
                          'Loaded conversation: ${chat['title'] ?? 'Untitled'}',
                      isUser: false,
                    ));
                  }
                } else {
                  // Add a system message if no history is available
                  provider.addMessage(ChatMessage(
                    content:
                        'Loaded conversation: ${chat['title'] ?? 'Untitled'}',
                    isUser: false,
                  ));
                }
              });

              // Debug the messages list after setState using microtask to ensure setState is processed
              Future.microtask(() {
                Logger.info(
                    'Messages list after setState: ${messages.length} messages');
                for (int i = 0; i < messages.length; i++) {
                  Logger.info('Message $i: ${messages[i].content}');
                }
              });
            } else {
              // Show error message
              setState(() {
                isLoading = false;
                provider.addMessage(ChatMessage(
                  content: 'Error loading conversation: ${result['message']}',
                  isUser: false,
                ));
              });

              // Debug error message
              Future.microtask(() {
                Logger.info('Error message added: ${messages.length} messages');
                if (messages.isNotEmpty) {
                  Logger.info('Error message: ${messages.last.content}');
                }
              });
            }
          }).catchError((error) {
            // Handle error
            setState(() {
              isLoading = false;
              provider.addMessage(ChatMessage(
                content: 'Error: $error',
                isUser: false,
              ));
            });

            // Debug catch error message
            Future.microtask(() {
              Logger.info(
                  'Catch error message added: ${messages.length} messages');
              if (messages.isNotEmpty) {
                Logger.info('Catch error message: ${messages.last.content}');
              }
            });
          });
        }
      },
      formatTimestamp: _formatTimestamp,
    );
  }

  // Format timestamp for display
  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return '';

    try {
      // Try to parse the timestamp
      DateTime dateTime;
      if (timestamp is String) {
        if (timestamp.contains('T')) {
          dateTime = DateTime.parse(timestamp);
        } else {
          // If it's a Unix timestamp as string
          dateTime =
              DateTime.fromMillisecondsSinceEpoch(int.parse(timestamp) * 1000);
        }
      } else if (timestamp is int) {
        // If it's a Unix timestamp as int
        dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
      } else if (timestamp is double) {
        // If it's a Unix timestamp as double
        dateTime =
            DateTime.fromMillisecondsSinceEpoch((timestamp * 1000).toInt());
      } else {
        return '';
      }

      // Format the date
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inDays == 0) {
        // Today - show time
        return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      } else if (difference.inDays == 1) {
        // Yesterday
        return 'Yesterday';
      } else if (difference.inDays < 7) {
        // This week - show day name
        final dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
        return dayNames[dateTime.weekday - 1];
      } else {
        // Older - show date
        return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
      }
    } catch (e) {
      return '';
    }
  }

  // Handle the conversation flow
  Future<void> _handleConversation(String text) async {
    final provider = Provider.of<WebHomeProvider>(context, listen: false);

    // Check if we already have a session ID
    if (provider.currentSessionId != null) {
      // Use the existing session ID
      await _sendConversationInput(provider.currentSessionId!, text);
    } else {
      // Create a new session
      _createNewSession(text);
    }
  }

  // Create a new conversation session
  Future<void> _createNewSession(String text) async {
    final provider = Provider.of<WebHomeProvider>(context, listen: false);

    try {
      final response = await provider.createNewConversationSession();

      if (!response['success']) {
        setState(() {
          isLoading = false;
          // Add error message
          provider.addMessage(ChatMessage(
            content: 'Error: ${response['message']}',
            isUser: false,
          ));
        });
      } else {
        // If successful, call the conversation API with the session ID and user input
        // Note: We don't set isLoading = false here because we're making another API call
        final sessionId = response['session_id'];
        await _sendConversationInput(sessionId, text);
      }

      // Scroll to bottom after the UI updates
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    } catch (error) {
      setState(() {
        isLoading = false;
        provider.addMessage(ChatMessage(
          content: 'Error: $error',
          isUser: false,
        ));
      });

      // Scroll to bottom after the UI updates
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    }
  }

  // Send input to an existing conversation
  Future<void> _sendConversationInput(String sessionId, String text) async {
    final provider = Provider.of<WebHomeProvider>(context, listen: false);

    // Get the last user message to check for file data
    Map<String, dynamic>? fileData;
    if (messages.isNotEmpty &&
        messages.last.isUser &&
        messages.last.fileData != null) {
      fileData = messages.last.fileData;
      Logger.info('Found file data in last message: ${fileData?['fileName']}');
    }

    // Use the lastUserMessageForApi if available, otherwise use the original text
    final String messageToSend = provider.lastUserMessageForApi.isNotEmpty
        ? provider.lastUserMessageForApi
        : text;

    Logger.info('Sending message to API: $messageToSend');

    try {
      final convResponse = await provider
          .sendConversationInput(sessionId, messageToSend, fileData: fileData);

      // Reset the lastUserMessageForApi after sending
      provider.lastUserMessageForApi = '';

      // Process workflow data first if available
      bool hasWorkflowData = false;
      Agent? workflowAgent;
      Agent? entityAgent;

      if (convResponse['success']) {
        final data = convResponse['data'];
        final conversationResponse = ConversationResponse.fromJson(data);

        // Check for workflow data in the response
        if (conversationResponse.workflowResult?.agentResults != null) {
          for (var agent
              in conversationResponse.workflowResult!.agentResults!) {
            if (agent.result?.workFlow?.workFlowDetails != null) {
              hasWorkflowData = true;
            }
            if (agent.agentName == "WORKFLOW") {
              workflowAgent = agent;
            }
            if (agent.agentName == "ENTITY") {
              entityAgent = agent;
            }
          }
        }
      }

      // Now update the UI
      setState(() {
        isLoading = false;

        if (convResponse['success']) {
          // Parse the response using the model
          final data = convResponse['data'];
          final conversationResponse = ConversationResponse.fromJson(data);

          if (conversationResponse.classification != null &&
              conversationResponse.classification!.explanation != null &&
              conversationResponse.workflowResult?.agentResults != null) {
            // Create a combined response for multiple response types
            String combinedContent = "";
            List<Widget> customContentWidgets = [];
            Agent? tenantLayerAgent;
            if (conversationResponse.workflowResult?.agentResults != null) {
              // Log all agent names for debugging
              for (var agent
                  in conversationResponse.workflowResult!.agentResults!) {
                // Check if this is the TENANT_LAYER agent and log its structure
                if (agent.agentName == "TENANT_LAYER") {
                  // Check if result is available
                  if (agent.result != null) {
                    tenantLayerAgent = agent;
                    break;
                  }
                }
              }
            }

            bool hasShownAnyResponse = false;

            // Add explanation content if available
            if (conversationResponse.classification?.explanation != null) {
              combinedContent =
                  conversationResponse.classification!.explanation!;
              hasShownAnyResponse = true;
            }
            if (tenantLayerAgent != null) {
              customContentWidgets.add(_buildRolesResponse());
              hasShownAnyResponse = true;
            }

            // Process entity data if available
            if (entityAgent != null && entityAgent.result?.entity != null) {
              // Extract entity data from the response
              EntitiesData extractedEntitiesData =
                  _extractEntitiesDataFromResponse(entityAgent.result!.entity!);

              // Update the global entities data
              globalEntitiesData = extractedEntitiesData;

              // Show the entities response
              customContentWidgets.add(_buildEntitiesResponse());
              hasShownAnyResponse = true;
            }

            // If we have workflow data, we'll process it outside setState
            // Just set a flag to indicate we'll process workflow data later
            bool hasWorkflowToProcess = (workflowAgent != null &&
                    workflowAgent.result?.workFlow != null) ||
                hasWorkflowData;

            if (hasWorkflowToProcess) {
              hasShownAnyResponse = true;
            }

            // If we have any response to show
            if (hasShownAnyResponse) {
              // Create a combined message with all response types
              if (customContentWidgets.isNotEmpty || hasWorkflowToProcess) {
                // If we have custom content or will add workflow later,
                // create a message with combined content
                provider.addMessage(ChatMessage(
                  content: combinedContent,
                  isUser: false,
                  // Only add custom content if we have entity data
                  // Workflow data will be added separately outside setState
                  customContent: customContentWidgets.isNotEmpty
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: customContentWidgets,
                        )
                      : null,
                ));
              } else {
                // If we only have text content
                provider.addMessage(ChatMessage(
                  content: combinedContent,
                  isUser: false,
                ));
              }
            } else {
              // If no responses were shown, add a generic message
              provider.addMessage(ChatMessage(
                content:
                    "I've processed your request but couldn't find specific data to display.",
                isUser: false,
              ));
            }
          } else if (conversationResponse.classification?.explanation != null) {
            // Add the explanation to the chat
            provider.addMessage(ChatMessage(
              content: conversationResponse.classification!.explanation!,
              isUser: false,
            ));
          } else {
            // Add a generic message if no explanation is available
            provider.addMessage(ChatMessage(
              content: "I've processed your request.",
              isUser: false,
            ));
          }
        } else {
          // Add error message
          provider.addMessage(ChatMessage(
            content: 'Error: ${convResponse['message']}',
            isUser: false,
          ));
        }
      });

      // Process workflow data outside of setState if available
      if (convResponse['success'] && hasWorkflowData) {
        final data = convResponse['data'];
        final conversationResponse = ConversationResponse.fromJson(data);
        bool hasWorkflowData = false;
        Agent? workflowAgent;

        if (conversationResponse.workflowResult?.agentResults != null) {
          // Check for workflow data
          for (var agent
              in conversationResponse.workflowResult!.agentResults!) {
            if (agent.result?.workFlow?.workFlowDetails != null) {
              hasWorkflowData = true;
            }
            if (agent.agentName == "WORKFLOW") {
              workflowAgent = agent;
            }
          }

          // Process workflow data if available
          if ((workflowAgent != null &&
                  workflowAgent.result?.workFlow != null) ||
              hasWorkflowData) {
            try {
              // Extract workflow data from the API response

              await _updateWorkflowsFromApiResponse(
                  conversationResponse, provider);

              // Check if the widget is still mounted
              if (!mounted) return;

              // Add workflow message using the direct data from API
              setState(() {
                messages.add(ChatMessage(
                  content: '',
                  isUser: false,
                  customContent: _buildWorkflowsResponseDirect(),
                ));
              });
            } catch (e) {
              // Show a message indicating workflow data couldn't be processed
              setState(() {
                messages.add(ChatMessage(
                  content: 'Workflow data is not available at this time.',
                  isUser: false,
                ));
              });
            }
          }
        }
      }

      // Scroll to bottom after the UI updates
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    } catch (error) {
      setState(() {
        isLoading = false;
        provider.addMessage(ChatMessage(
          content: 'Error sending conversation input: $error',
          isUser: false,
        ));
      });

      // Scroll to bottom after the UI updates
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    }
  }

  // Method to toggle OCR panel visibility
  void toggleOcrPanel() {
    setState(() {
      showOcrPanel = !showOcrPanel;
    });
  }

  // Extract agent data from API response
  AgentData _extractAgentDataFromResponse(Agent agent) {
    try {
      Logger.info(
          '🔍 DATA SOURCE CHECK: Extracting agent data from API response');
      print('🔍 DATA SOURCE CHECK: Extracting agent data from API response');
      Logger.info(
          '🔍 DATA SOURCE CHECK: Processing agent with name: ${agent.agentName}');
      print(
          '🔍 DATA SOURCE CHECK: Processing agent with name: ${agent.agentName}');

      // Create a new AgentData object
      AgentData agentData = AgentData();
      List<AgentInfo> agentInfoList = [];

      // Check if result is available
      if (agent.result == null) {
        Logger.info(
            '🔍 DATA SOURCE CHECK: Agent result is null, returning empty AgentData');
        print(
            '🔍 DATA SOURCE CHECK: Agent result is null, returning empty AgentData');
        return agentData;
      }

      // Log all available fields in the result for debugging
      final resultJson = agent.result!.toJson();
      Logger.info(
          '🔍 DATA SOURCE CHECK: Agent result fields: ${resultJson.keys.join(', ')}');
      print(
          '🔍 DATA SOURCE CHECK: Agent result fields: ${resultJson.keys.join(', ')}');

      // Process standard agents field if available
      if (agent.result?.agents != null && agent.result!.agents!.isNotEmpty) {
        Logger.info(
            '🔍 DATA SOURCE CHECK: Processing standard agents field with ${agent.result!.agents!.length} agents');
        print(
            '🔍 DATA SOURCE CHECK: Processing standard agents field with ${agent.result!.agents!.length} agents');

        for (var agentElement in agent.result!.agents!) {
          // Create agent info from agent element
          AgentInfo agentInfo = _createAgentInfoFromElement(agentElement);
          agentInfoList.add(agentInfo);
          Logger.info(
              '🔍 DATA SOURCE CHECK: Added agent info from standard agents field: ${agentElement.title}');
          print(
              '🔍 DATA SOURCE CHECK: Added agent info from standard agents field: ${agentElement.title}');
        }
      }
      // Check for alternative fields that might contain agent-related data
      else {
        Logger.info(
            '🔍 No standard agents field found, checking for alternative data');

        // Try to extract agent data from other fields
        final fieldsToCheck = [
          'business_logic',
          'core_components',
          'data_flow',
          'decision_points',
          'error_handling',
          'interfaces'
        ];

        List<AgentSection> sections = [];

        for (var field in fieldsToCheck) {
          if (resultJson.containsKey(field) && resultJson[field] is List) {
            final items = List<String>.from(resultJson[field]);
            if (items.isNotEmpty) {
              Logger.info('🔍 Found ${items.length} items in ${field} field');

              // Create a section for this field
              sections.add(AgentSection(
                id: 'section_${field}_${DateTime.now().millisecondsSinceEpoch}',
                title: field.replaceAll('_', ' ').toUpperCase(),
                abbreviation: field.substring(0, 2).toUpperCase(),
                items: items,
              ));
            }
          }
        }

        if (sections.isNotEmpty) {
          Logger.info(
              '🔍 Created ${sections.length} sections from alternative fields');

          // Create a synthetic agent element
          final syntheticElement = AgentElement(
            id: 'agent_${DateTime.now().millisecondsSinceEpoch}',
            title: '${agent.agentName} Information',
            description: 'Information extracted from ${agent.agentName} agent',
            version: '1.0',
            sections: sections,
          );

          // Create agent info from synthetic element
          AgentInfo agentInfo = AgentInfo(
            id: syntheticElement.id ??
                'agent_${DateTime.now().millisecondsSinceEpoch}',
            title: syntheticElement.title ?? 'Untitled Agent',
            description: syntheticElement.description ?? '',
            version: syntheticElement.version ?? '1.0',
            sections: _processAgentSections(syntheticElement.sections),
          );

          agentInfoList.add(agentInfo);
        }
      }

      // Create system info
      AgentSystemInfo systemInfo = AgentSystemInfo(
        agentCount: agentInfoList.length,
        bulletPoints: [
          "Agent data extracted from API response",
          "Contains ${agentInfoList.length} agents",
          "Processed from ${agent.agentName} agent"
        ],
        headerText: "Agent Information from ${agent.agentName}",
      );

      // Update the AgentData object
      agentData = AgentData(
        agents: agentInfoList,
        systemInfo: systemInfo,
      );

      Logger.info(
          '🔍 API SUCCESS: Extracted agent data from API response with ${agentInfoList.length} agents');
      return agentData;
    } catch (e) {
      Logger.error('Error extracting agent data from response: $e');
      // Return empty AgentData in case of error
      return AgentData();
    }
  }

  // Helper method to create AgentInfo from AgentElement
  AgentInfo _createAgentInfoFromElement(AgentElement agentElement) {
    return AgentInfo(
      id: agentElement.id ?? 'agent_${DateTime.now().millisecondsSinceEpoch}',
      title: agentElement.title ?? 'Untitled Agent',
      description: agentElement.description ?? '',
      version: agentElement.version ?? '1.0',
      createdBy: agentElement.createdBy ?? '',
      createdDate: agentElement.createdDate != null
          ? _parseDate(agentElement.createdDate!)
          : null,
      modifiedBy: agentElement.modifiedBy ?? '',
      modifiedDate: agentElement.modifiedDate != null
          ? _parseDate(agentElement.modifiedDate!)
          : null,
      sections: _processAgentSections(agentElement.sections),
    );
  }

  // Extract roles data from tenant_layer agent response is now handled by the agent_data.dart model

  // Update roles data from agent data
  void _updateRolesFromAgentData(AgentData agentData) {
    try {
      Logger.info(
          '🔍 DATA SOURCE CHECK: Starting to update roles from agent data');
      print('🔍 DATA SOURCE CHECK: Starting to update roles from agent data');

      if (agentData.agents == null || agentData.agents!.isEmpty) {
        Logger.info(
            '🔍 DATA SOURCE CHECK: No agent data available to update roles - would use fallback');
        print(
            '🔍 DATA SOURCE CHECK: No agent data available to update roles - would use fallback');
        return;
      }

      // Clear existing roles
      roles.clear();

      // Create standard roles structure regardless of API data
      // This ensures we always have the three expected roles

      // Extract data from agent sections to use in our roles
      List<String> allUseCases = [];
      List<String> allEntities = [];
      List<String> allObjectives = [];

      // Process all agent data to collect use cases and permissions
      Logger.info(
          '🔍 DATA SOURCE CHECK: Processing ${agentData.agents!.length} agents from API data');
      print(
          '🔍 DATA SOURCE CHECK: Processing ${agentData.agents!.length} agents from API data');

      for (var agent in agentData.agents!) {
        Logger.info(
            '🔍 DATA SOURCE CHECK: Processing agent: ${agent.title} with ${agent.sections.length} sections');
        print(
            '🔍 DATA SOURCE CHECK: Processing agent: ${agent.title} with ${agent.sections.length} sections');

        for (var section in agent.sections) {
          Logger.info(
              '🔍 DATA SOURCE CHECK: Processing section: ${section.title} with ${section.items.length} items');
          print(
              '🔍 DATA SOURCE CHECK: Processing section: ${section.title} with ${section.items.length} items');

          if (section.title.toUpperCase().contains('BUSINESS') ||
              section.title.toUpperCase().contains('COMPONENT') ||
              section.title.toUpperCase().contains('INTERFACE')) {
            allUseCases.addAll(section.items);
            Logger.info(
                '🔍 DATA SOURCE CHECK: Added ${section.items.length} items to useCases from API');
            print(
                '🔍 DATA SOURCE CHECK: Added ${section.items.length} items to useCases from API');
          } else if (section.title.toUpperCase().contains('DATA') ||
              section.title.toUpperCase().contains('FLOW')) {
            allEntities.addAll(section.items);
            Logger.info(
                '🔍 DATA SOURCE CHECK: Added ${section.items.length} items to entities from API');
            print(
                '🔍 DATA SOURCE CHECK: Added ${section.items.length} items to entities from API');
          } else {
            allObjectives.addAll(section.items);
            Logger.info(
                '🔍 DATA SOURCE CHECK: Added ${section.items.length} items to objectives from API');
            print(
                '🔍 DATA SOURCE CHECK: Added ${section.items.length} items to objectives from API');
          }
        }
      }

      // Log the data collected from API
      Logger.info(
          '🔍 DATA SOURCE CHECK: Collected from API - UseCases: ${allUseCases.length}, Entities: ${allEntities.length}, Objectives: ${allObjectives.length}');
      print(
          '🔍 DATA SOURCE CHECK: Collected from API - UseCases: ${allUseCases.length}, Entities: ${allEntities.length}, Objectives: ${allObjectives.length}');

      // Ensure we have at least some default values if API data is sparse
      if (allUseCases.isEmpty) {
        allUseCases = [
          "Can access Claims Processing",
          "Claims Investigation and Claims Recovery"
        ];
        Logger.info(
            '🔍 DATA SOURCE CHECK: Using FALLBACK for useCases because API data was empty');
        print(
            '🔍 DATA SOURCE CHECK: Using FALLBACK for useCases because API data was empty');
      } else {
        Logger.info('🔍 DATA SOURCE CHECK: Using API DATA for useCases');
        print('🔍 DATA SOURCE CHECK: Using API DATA for useCases');
        // Print first few items for verification
        for (int i = 0;
            i < (allUseCases.length > 3 ? 3 : allUseCases.length);
            i++) {
          Logger.info(
              '🔍 DATA SOURCE CHECK: UseCase ${i + 1}: ${allUseCases[i]}');
          print('🔍 DATA SOURCE CHECK: UseCase ${i + 1}: ${allUseCases[i]}');
        }
      }

      if (allEntities.isEmpty) {
        allEntities = [
          "Can Read entity Policy",
          "Can Read entity Customer",
          "Can Read, Create, Update, Delete entity Claim",
          "Can Read, Create, Update, Delete entity ClaimNote",
          "Can Read, Create, Update, Delete entity ClaimDocument",
          "Can Read, Create, Update, Delete entity ClaimPayment",
          "Can Read entity Coverage"
        ];
        Logger.info(
            '🔍 DATA SOURCE CHECK: Using FALLBACK for entities because API data was empty');
        print(
            '🔍 DATA SOURCE CHECK: Using FALLBACK for entities because API data was empty');
      } else {
        Logger.info('🔍 DATA SOURCE CHECK: Using API DATA for entities');
        print('🔍 DATA SOURCE CHECK: Using API DATA for entities');
        // Print first few items for verification
        for (int i = 0;
            i < (allEntities.length > 3 ? 3 : allEntities.length);
            i++) {
          Logger.info(
              '🔍 DATA SOURCE CHECK: Entity ${i + 1}: ${allEntities[i]}');
          print('🔍 DATA SOURCE CHECK: Entity ${i + 1}: ${allEntities[i]}');
        }
      }

      if (allObjectives.isEmpty) {
        allObjectives = [
          "Create Claim",
          "Assign Claim",
          "Review Claim",
          "Approve Claim",
          "Reject Claim",
          "Close Claim",
          "Initiate Investigation",
          "Assign Investigator",
          "Collect Evidence"
        ];
        Logger.info(
            '🔍 DATA SOURCE CHECK: Using FALLBACK for objectives because API data was empty');
        print(
            '🔍 DATA SOURCE CHECK: Using FALLBACK for objectives because API data was empty');
      } else {
        Logger.info('🔍 DATA SOURCE CHECK: Using API DATA for objectives');
        print('🔍 DATA SOURCE CHECK: Using API DATA for objectives');
        // Print first few items for verification
        for (int i = 0;
            i < (allObjectives.length > 3 ? 3 : allObjectives.length);
            i++) {
          Logger.info(
              '🔍 DATA SOURCE CHECK: Objective ${i + 1}: ${allObjectives[i]}');
          print(
              '🔍 DATA SOURCE CHECK: Objective ${i + 1}: ${allObjectives[i]}');
        }
      }

      // Create the three standard roles with data from API
      Logger.info(
          '🔍 DATA SOURCE CHECK: Creating standard roles with API data');
      print('🔍 DATA SOURCE CHECK: Creating standard roles with API data');

      // 1. CRM Administrator
      roles.add(RoleInfo(
        id: "id0001",
        title: "CRM Administrator",
        description:
            "Manages system configuration, user accounts, and has full access to all functions.",
        useCases: allUseCases,
        permissions: {
          'entities': allEntities,
          'objectives': allObjectives,
        },
        version: "V00012",
        createdBy: "John Smith",
        createdDate: "12/04/2023",
        modifiedBy: "Jane Doe",
        modifiedDate: "25/04/2023",
      ));

      // 2. Senior Claims Adjuster
      roles.add(RoleInfo(
        id: "id0002",
        title: "Senior Claims Adjuster",
        description:
            "Handles complex claims, manages claim investigations, and provides technical expertise.",
        useCases: allUseCases,
        permissions: {
          'entities': allEntities.sublist(
              0, allEntities.length > 5 ? 5 : allEntities.length),
          'objectives': allObjectives.sublist(
              0, allObjectives.length > 5 ? 5 : allObjectives.length),
        },
        version: "V00012",
        createdBy: "John Smith",
        createdDate: "12/04/2023",
        modifiedBy: "Jane Doe",
        modifiedDate: "25/04/2023",
      ));

      // 3. Claims Adjuster
      roles.add(RoleInfo(
        id: "id0003",
        title: "Claims Adjuster",
        description:
            "Claims Adjuster Processes standard claims, communicates with claimants, and determines coverage.",
        useCases: allUseCases,
        permissions: {
          'entities': allEntities.sublist(
              0, allEntities.length > 3 ? 3 : allEntities.length),
          'objectives': allObjectives.sublist(
              0, allObjectives.length > 2 ? 2 : allObjectives.length),
        },
        version: "V00012",
        createdBy: "John Smith",
        createdDate: "12/04/2023",
        modifiedBy: "Jane Doe",
        modifiedDate: "25/04/2023",
      ));

      // Update role system info with standard bullet points
      roleSystemInfo = {
        'roleCount': roles.length,
        'hierarchyLevels': 3,
        'bulletPoints': [
          'System Administrator has full system access.',
          'Senior Claims Adjuster has high approval thresholds.',
          'Claims Adjuster has limited approval authority.'
        ],
      };

      // Set loading flag to false
      isLoadingRoles = false;

      Logger.info(
          '🔍 DATA SOURCE CHECK: Successfully created ${roles.length} roles with data from API');
      print(
          '🔍 DATA SOURCE CHECK: Successfully created ${roles.length} roles with data from API');
    } catch (e) {
      Logger.error('Error updating roles from agent data: $e');
      print('🔍 DATA SOURCE CHECK: ERROR updating roles from agent data: $e');
      // Keep existing roles if there's an error
    }
  }

  // Helper method to process agent sections
  List<AgentInfoSection> _processAgentSections(List<AgentSection>? sections) {
    if (sections == null || sections.isEmpty) {
      return [];
    }

    List<AgentInfoSection> result = [];

    for (var section in sections) {
      // Process tabs if available
      List<AgentInfoTab>? tabs;
      if (section.tabs != null && section.tabs!.isNotEmpty) {
        tabs = section.tabs!
            .map((tab) => AgentInfoTab(
                  name: tab.name,
                  isSelected: tab.isSelected,
                  data: tab.data?.cast<String>(),
                ))
            .toList();
      }

      // Create agent section
      AgentInfoSection agentSection = AgentInfoSection(
        id: section.id ?? 'section_${DateTime.now().millisecondsSinceEpoch}',
        title: section.title ?? 'Untitled Section',
        abbreviation: section.abbreviation ?? '',
        items: section.items?.cast<String>() ?? [],
        tabs: tabs,
      );

      result.add(agentSection);
    }

    return result;
  }

  // Build agent response UI from provided agent data
  Widget _buildAgentResponse(AgentData agentData) {
    // Show loading indicator if agent data is empty
    if (agentData.agents == null || agentData.agents!.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Loading agent data...',
              style: TextStyle(
                fontFamily: 'TiemposText',
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Text(
                "Agent Information",
                style: TextStyle(
                  fontFamily: 'TiemposText',
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ),
            SvgPicture.asset('assets/images/eye.svg', height: 14, width: 18),
            SizedBox(width: 5),
            Text(
              'PREVIEW',
              style: TextStyle(
                fontSize: 9,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),

        SizedBox(height: AppSpacing.md),

        // Agent data container
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(AppSpacing.xs),
            color: Colors.white,
          ),
          padding: EdgeInsets.all(AppSpacing.md),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: (agentData.agents ?? []).map((agent) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Agent title
                  Text(
                    agent.title,
                    style: TextStyle(
                      fontFamily: 'TiemposText',
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),

                  SizedBox(height: AppSpacing.xs),

                  // Agent description
                  if (agent.description.isNotEmpty)
                    Text(
                      agent.description,
                      style: TextStyle(
                        fontFamily: 'TiemposText',
                        fontSize: 14,
                      ),
                    ),

                  SizedBox(height: AppSpacing.md),

                  // Agent sections
                  ...agent.sections.map((section) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Section title
                        Text(
                          section.title,
                          style: TextStyle(
                            fontFamily: 'TiemposText',
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),

                        SizedBox(height: AppSpacing.xs),

                        // Section items
                        ...section.items.map((item) {
                          return Padding(
                            padding: EdgeInsets.only(left: 16, bottom: 4),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('• ',
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold)),
                                Expanded(
                                  child: Text(
                                    item,
                                    style: TextStyle(
                                      fontFamily: 'TiemposText',
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),

                        SizedBox(height: AppSpacing.sm),
                      ],
                    );
                  }).toList(),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  // Build agent response UI from global agent data is now handled by _buildAgentResponse

  // Helper method to parse dates in different formats
  DateTime _parseDate(dynamic dateInput) {
    // Default date to return if parsing fails
    final defaultDate = DateTime(2000, 1, 1);

    try {
      // If null, return default date
      if (dateInput == null) {
        return defaultDate;
      }

      // If already a DateTime, return it
      if (dateInput is DateTime) {
        return dateInput;
      }

      // If it's a String, try to parse it
      if (dateInput is String) {
        try {
          // Try standard ISO format first
          return DateTime.parse(dateInput);
        } catch (e) {
          try {
            // Try DD/MM/YYYY format
            final parts = dateInput.split('/');
            if (parts.length == 3) {
              final day = int.parse(parts[0]);
              final month = int.parse(parts[1]);
              final year = int.parse(parts[2]);
              return DateTime(year, month, day);
            }
          } catch (e) {
            // Log the error but don't throw
            Logger.error('Error parsing date "$dateInput": $e');
          }

          try {
            // Try MM/DD/YYYY format
            final parts = dateInput.split('/');
            if (parts.length == 3) {
              final month = int.parse(parts[0]);
              final day = int.parse(parts[1]);
              final year = int.parse(parts[2]);
              return DateTime(year, month, day);
            }
          } catch (e) {
            // Log the error but don't throw
            Logger.error('Error parsing date "$dateInput" as MM/DD/YYYY: $e');
          }
        }
      }

      // If we get here, all parsing attempts failed
      return defaultDate;
    } catch (e) {
      // Catch any unexpected errors
      Logger.error('Unexpected error parsing date: $e');
      return defaultDate;
    }
  }

  Widget hoverButtons({icon, onPressed}) {
    if (icon is Icon && icon.icon == Icons.add) {
      return _AddButtonWithMenu(this);
    }
    return _HoverButton(
      icon: icon,
      onPressed: onPressed,
    );
  }

  // Build a message bubble for chat
  Widget _buildMessageBubble(ChatMessage message, int index) {
    // Debug the message content
    Logger.info('Building message bubble with content: "${message.content}"');

    // Initialize expansion state for this message if not already set
    if (!nslThinkingExpanded.containsKey(index)) {
      nslThinkingExpanded[index] = false;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (message.isUser)
          // Use IntrinsicWidth to make the container take only the width it needs
          Align(
            alignment: Alignment.centerLeft,
            child: Container(
              // Remove SizedBox wrapper and use constraints to limit max width
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width *
                    0.7, // Limit to 70% of screen width
              ),
              decoration: BoxDecoration(
                color: Color(0xFFE9F2F7),
                borderRadius: BorderRadius.circular(AppSpacing.xxs),
              ),
              // margin: EdgeInsets.only(left: 24),
              padding: EdgeInsets.only(
                left: AppSpacing.xs,
                right: 20,
                top: AppSpacing.xs,
                bottom: AppSpacing.xs,
              ),

              child: Row(
                mainAxisSize:
                    MainAxisSize.min, // This makes the Row take minimum width
                crossAxisAlignment:
                    CrossAxisAlignment.center, // Center align items vertically
                mainAxisAlignment:
                    MainAxisAlignment.start, // Start align items horizontally
                children: [
                  // Adjust the avatar to match the roles table alignment
                  SizedBox(
                    width: 30, // Match the checkbox width in roles table
                    child: CircleAvatar(
                      backgroundColor: Color(0xFF0058FF),
                      radius: 12,
                      child: Text(
                        'D',
                        style: TextStyle(
                          // fontFamily: 'TiemposText',
                          color: Colors.white,
                          fontSize: MediaQuery.of(context).size.width > 1600
                              ? 17
                              : 15,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: AppSpacing.xxs),
                  // Remove Expanded to allow natural width
                  Flexible(
                    child: Padding(
                      padding: EdgeInsets.only(
                          top: 2), // Fine-tune vertical alignment
                      child: Text(
                        message.content,
                        textAlign: TextAlign.left,
                        style: TextStyle(
                          // fontFamily: ''
                          // letterSpacing: 1.2,
                          fontSize: MediaQuery.of(context).size.width > 1600
                              ? 17
                              : 15,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )
        else

          // NSL response
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // NSL Thinking expandable widget
              if (message.reasoningData != null &&
                  message.reasoningData!.isNotEmpty)
                Container(
                  alignment: Alignment.centerRight,
                  child: AnimatedContainer(
                    duration: Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    width: nslThinkingExpanded[index] == true
                        ? double.infinity
                        : null, // Dynamic width based on expansion state
                    margin: const EdgeInsets.only(top: AppSpacing.xs),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: CustomExpansionTile(
                      backgroundColor: Colors.grey.shade100,
                      initiallyExpanded: nslThinkingExpanded[index] == true,
                      onExpansionChanged: (expanded) {
                        setState(() {
                          // Update the expansion state in our map
                          nslThinkingExpanded[index] = expanded;
                        });
                      },
                      onTitleTap: () {
                        // This will be handled by the CustomExpansionTile
                      },
                      title: Container(
                        color: Colors.white,
                        padding: EdgeInsets.symmetric(
                            vertical: 12.0, horizontal: 12.0),
                        child: Text(
                          "NSL Knowledge",
                          style: TextStyle(
                            fontFamily: 'TiemposText',
                            fontSize: MediaQuery.of(context).size.width > 1600
                                ? 14
                                : 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      children: [
                        Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            // border: Border.all(
                            //   color: Colors.grey.shade200,
                            //   width: 1,
                            // ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: message.reasoningData!.map((reasoning) {
                              // The reasoning data should already be processed by WebHomeProvider
                              // and contain only the content strings
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 8.0),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '• ',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontFamily: 'TiemposText',
                                        fontSize:
                                            MediaQuery.of(context).size.width >
                                                    1600
                                                ? 16
                                                : 14,
                                      ),
                                    ),
                                    Expanded(
                                      child: Text(
                                        reasoning,
                                        style: TextStyle(
                                          fontFamily: 'TiemposText',
                                          fontSize: MediaQuery.of(context)
                                                      .size
                                                      .width >
                                                  1600
                                              ? 16
                                              : 14,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              Container(
                padding:
                    EdgeInsets.only(top: AppSpacing.xs, bottom: AppSpacing.xs),
                // constraints: BoxConstraints(
                //   maxWidth: MediaQuery.of(context).size.width * 0.8,
                // ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: _MessageContentWithHover(
                        message: message,
                        index: index,
                        audioPlayer: _multimediaService.audioPlayer,
                        currentPlayingMessageId:
                            _multimediaService.currentPlayingMessageId,
                        isPlaying: _multimediaService.isPlaying,
                        isPaused: _multimediaService.isPaused,
                        currentPosition: _multimediaService.currentPosition,
                        totalDuration: _multimediaService.totalDuration,
                        onTextToSpeech: _convertTextToSpeech,
                        onStopAudio: () => _multimediaService.stopAudio(),
                        showCopyOverlay: _showCopyOverlay,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: AppSpacing.xxs),
            ],
          ),
        SizedBox(
          height: AppSpacing.xs,
        )
      ],
    );
  }

//   // Roles are now loaded from JSON file in _loadRolesData()

  // Create a response with roles and use cases table
  Widget _buildRolesResponse() {
    // Show loading indicator while roles are being loaded
    if (isLoadingRoles) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Loading roles data...',
              style: TextStyle(
                fontFamily: 'TiemposText',
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with icon
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Consumer<AuthProvider>(
                builder: (context, authProvider, _) {
                  // Get the username from the user profile
                  final String firstName = authProvider.user?.username ?? '';

                  // Get the localized greeting text
                  final String greeting = AppLocalizations.of(context)
                      .translate('build.rolesGreeting');

                  // If we have a username, prepend it to the greeting
                  final String displayText =
                      firstName.isNotEmpty ? '$firstName, $greeting' : greeting;

                  return Text(
                    displayText,
                    style: TextStyle(
                      fontFamily: 'TiemposText',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  );
                },
              ),
            ),
            SvgPicture.asset('assets/images/icons/box.svg',
                // fit: BoxFit.fill,
                height: 24,
                width: 24),
          ],
        ),

        SizedBox(height: AppSpacing.xs),

        //Table with roles
        SizedBox(
          width: MediaQuery.of(context).size.width -
              (showSidePanel ? sidePanelWidth : 0) -
              48,
          child: Container(
            padding: EdgeInsets.all(
              AppSpacing.xxs,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Color(0xFFBEBEBE), width: 0.5),
              // borderRadius: BorderRadius.circular(AppSpacing.sm),
              // color: Colors.white,
            ),
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: Color(0xFFBEBEBE), width: 0.5),
                // borderRadius: BorderRadius.circular(AppSpacing.sm),
                // color: Colors.white,
              ),
              //  margin: EdgeInsets.only(bottom:20,),
              //  left:250), // Indent the table
              child: Column(
                children: [
                  // Header row
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSpacing.sm,
                      //  vertical: AppSpacing.xs
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(AppSpacing.xxs),
                        topRight: Radius.circular(AppSpacing.xxs),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        // SizedBox(width: 30),

                        Column(
                          children: [
                            SizedBox(height: AppSpacing.xxs),
                            SizedBox(
                              width: 20,
                              child: Checkbox(
                                value: false,
                                onChanged: (_) {},
                                materialTapTargetSize:
                                    MaterialTapTargetSize.shrinkWrap,
                                visualDensity: VisualDensity.compact,
                                side: BorderSide(
                                  color:
                                      Color(0xFFBEBEBE), // Custom border color
                                  width: 1, // Custom border width
                                ),
                              ),
                            ),
                          ],
                        ),
                        // SizedBox(width:12),
                        Container(
                          height: 40,
                          width: 1,
                          color: Colors.grey.shade300,
                          margin: EdgeInsets.symmetric(horizontal: 8),
                        ), // Checkbox space
                        Expanded(
                          child: Text(
                            'Roles And Usecases',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 12,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                        ),

                        Row(
                          children: [
                            Icon(Icons.folder_outlined,
                                size: 18, color: Colors.grey.shade600),
                            Icon(Icons.arrow_drop_down,
                                size: 18, color: Colors.grey.shade600),
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: AppSpacing.sm, vertical: 2),
                              child: Text(
                                'AGO0012',
                                style: TextStyle(fontSize: 12),
                              ),
                            ),
                            SizedBox(width: AppSpacing.sm),
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: AppSpacing.sm, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.amber.shade100,
                                borderRadius:
                                    BorderRadius.circular(AppSpacing.xxs),
                              ),
                              child: Text(
                                '3 Agent',
                                style: TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'TiemposText',
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Role rows - using Column instead of ListView.builder to avoid syntax issues
                  Column(
                    children: roles.map((role) {
                      // Explicitly check if this role is selected
                      final bool isSelected =
                          selectedRole != null && selectedRole!.id == role.id;

                      return BuildRoleCard(
                        role: role,
                        isSelected: isSelected,
                        onRoleTap: (selectedRole) {
                          setState(() {
                            this.selectedRole = selectedRole;
                            selectedEntity = null;
                            selectedWorkflow = null;
                            showSidePanel = true;
                          });
                        },
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),
        ),

        SizedBox(height: AppSpacing.xs),

        // Text below table
        Text(
          'System contains ${roleSystemInfo['roleCount'] ?? 3} roles in a ${roleSystemInfo['hierarchyLevels'] ?? 3}-level hierarchy. Role inheritance ensures proper permission\ndistribution.',
          style: TextStyle(
            fontFamily: 'TiemposText',
            fontSize: 14,
          ),
        ),
        SizedBox(height: AppSpacing.xxs),

        // Bullet points
        ...(roleSystemInfo['bulletPoints'] as List<dynamic>? ??
                [
                  'System Administrator has full system access.',
                  'Senior Claims Adjuster has high approval thresholds.',
                  'Claims Adjuster has limited approval authority.'
                ])
            .map(
          (text) =>
              //  Padding(
              // padding: EdgeInsets.only(
              //bottom: AppSpacing.xxs, ),
              //left: 150),
              //  child:
              Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('•     ',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
              Expanded(
                child: Text(
                  text,
                  style: TextStyle(
                    fontFamily: 'TiemposText',
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        ),
        //),
        SizedBox(height: AppSpacing.xs),

        // Question at the end
        Text(
          'Do you want to add new roles and it\'s use cases?',
          style: TextStyle(
            fontFamily: 'TiemposText',
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  // Build side panel for role details
  Widget _buildRoleDetailsPanel(RoleInfo role) {
    final ScrollController roleDetailScrollController = ScrollController();
    final Map<String, GlobalKey> roleSectionKeys = {
      'useCases': GlobalKey(),
      'permissions': GlobalKey(),
    };
    void scrollToRoleSection(String sectionId) {
      if (roleSectionKeys.containsKey(sectionId)) {
        final RenderObject? renderObject =
            roleSectionKeys[sectionId]?.currentContext?.findRenderObject();
        if (renderObject != null) {
          // RenderBox renderBox = renderObject as RenderBox;
          // _roleDetailScrollController.animateTo(renderBox.localToGlobal(Offset.zero).dy,  duration: const Duration(milliseconds: 300),
          //   curve: Curves.easeInOut,);
          roleDetailScrollController.position.ensureVisible(
            renderObject,
            alignment: 0.0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      }
    }

    return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            left: BorderSide(color: Colors.grey.shade300, width: 1),
          ),
          boxShadow: [
            BoxShadow(
              color:
                  Color(0xff9B9B9B).withValues(alpha: 0.14), // 0.14 * 255 = ~36
              blurRadius: 20,
              offset: Offset(-3, 0),
            ),
          ],
        ),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          // Header with role title and close button
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(width: 30),
                      CircleAvatar(
                        backgroundColor: Color(0xffE6F7FF),
                        //   const Color.fromARGB(255, 92, 163, 234),
                        radius: 10,
                        child: Icon(
                          Icons.person_outline,
                          color: Color(0xff1890FF),
                          size: 16,
                        ),
                      ),
                      SizedBox(width: AppSpacing.xxs),
                      Expanded(
                        child: Text(
                          role.title,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            fontFamily: "TiemposText",
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.chat, color: Colors.black, size: 16),
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(),
                  onPressed: () {
                    setState(() {
                      showSidePanel = false;
                    });
                  },
                ),
              ],
            ),
          ),

          // Content area with UC and PR labels
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left column for UC and PR labels
                Container(
                  width: 30,
                  padding: EdgeInsets.only(top: 3),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // UC label aligned with Use cases section
                      Padding(
                        padding: EdgeInsets.only(left: 8, top: 16),
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => scrollToRoleSection('useCases'),
                            child: Text(
                              'UC',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 10,
                                color: Colors.grey.shade700,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ),
                      ),
                      // Space to align PR with Permissions section
                      SizedBox(
                          height:
                              10), // Adjust this height based on your content
                      // PR label aligned with Permissions section
                      Padding(
                        padding: EdgeInsets.only(left: 8),
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => scrollToRoleSection('permissions'),
                            child: Text(
                              'PR',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 10,
                                color: Colors.grey.shade700,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Right column for content - keep your existing content structure
                Expanded(
                  child: SingleChildScrollView(
                    controller: roleDetailScrollController,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Use cases section - keep your existing implementation
                        Container(
                          key: roleSectionKeys['useCases'],
                          padding: EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                'Use cases',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                  fontFamily: "TiemposText",
                                ),
                              ),
                              SizedBox(height: 8),
                              if (role.useCases != null &&
                                  role.useCases!.isNotEmpty)
                                RichText(
                                  text: TextSpan(
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontFamily: "TiemposText",
                                      color: Colors.black,
                                    ),
                                    children: [
                                      // Role title with tooltip - keep your existing tooltip
                                      WidgetSpan(
                                        child: MouseRegion(
                                          // cursor: SystemMouseCursors.click,
                                          child: Tooltip(
                                            richMessage: WidgetSpan(
                                              child: Container(
                                                margin: EdgeInsets.only(
                                                  left: 170,
                                                ),
                                                width: 380,
                                                decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                  boxShadow: [
                                                    BoxShadow(
                                                      color: Colors.black
                                                          .withValues(
                                                              alpha:
                                                                  51), // 0.2 * 255 = ~51
                                                      blurRadius: 10,
                                                      offset: Offset(0, 3),
                                                    ),
                                                  ],
                                                ),
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    // Blue header with icon
                                                    Container(
                                                      padding:
                                                          EdgeInsets.all(12),
                                                      decoration: BoxDecoration(
                                                        color: Colors
                                                            .blue.shade700,
                                                        borderRadius:
                                                            BorderRadius.only(
                                                          topLeft:
                                                              Radius.circular(
                                                                  8),
                                                          topRight:
                                                              Radius.circular(
                                                                  8),
                                                        ),
                                                      ),
                                                      child: Row(
                                                        children: [
                                                          Container(
                                                            width: 20,
                                                            height: 20,
                                                            decoration:
                                                                BoxDecoration(
                                                              border:
                                                                  Border.all(
                                                                color: Colors
                                                                    .white,
                                                                width: 1.5,
                                                              ),
                                                              color: Colors.blue
                                                                  .shade700,
                                                              shape: BoxShape
                                                                  .circle,
                                                            ),
                                                            child: Icon(
                                                              Icons
                                                                  .person_outline,
                                                              color:
                                                                  Colors.white,
                                                              size: 18,
                                                            ),
                                                          ),
                                                          SizedBox(
                                                              width: AppSpacing
                                                                  .xs),
                                                          Text(
                                                            'Administrator',
                                                            style: TextStyle(
                                                              color:
                                                                  Colors.white,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              fontFamily:
                                                                  'TiemposText',
                                                              fontSize: 12,
                                                            ),
                                                          ),

                                                          // Column(
                                                          //   crossAxisAlignment:
                                                          //       CrossAxisAlignment
                                                          //           .start,
                                                          //   children: [
                                                          //     Row(
                                                          //       children: [
                                                          //         Text(
                                                          //           '• ID: ${role.id}',
                                                          //           style:
                                                          //               TextStyle(
                                                          //             color: Colors
                                                          //                 .white,
                                                          //             fontSize: 12,
                                                          //             fontWeight:
                                                          //                 FontWeight
                                                          //                     .w500,
                                                          //             fontFamily:
                                                          //                 'TiemposText',
                                                          //           ),
                                                          //         ),
                                                          //         SizedBox(
                                                          //             width: 8),
                                                          //         Text(
                                                          //           '• Version: ${role.version}',
                                                          //           style:
                                                          //               TextStyle(
                                                          //             color: Colors
                                                          //                 .white,
                                                          //             fontSize: 12,
                                                          //             fontWeight:
                                                          //                 FontWeight
                                                          //                     .w500,
                                                          //             fontFamily:
                                                          //                 'TiemposText',
                                                          //           ),
                                                          //         ),
                                                          //       ],
                                                          //     ),
                                                          //     SizedBox(height: 4),
                                                          //     Row(
                                                          //       children: [
                                                          //         Text(
                                                          //           '• Created: ${role.createdDate} by ${role.createdBy}',
                                                          //           style:
                                                          //               TextStyle(
                                                          //             color: Colors
                                                          //                 .white,
                                                          //             fontSize: 12,
                                                          //             fontWeight:
                                                          //                 FontWeight
                                                          //                     .w500,
                                                          //             fontFamily:
                                                          //                 'TiemposText',
                                                          //           ),
                                                          //         ),
                                                          //       ],
                                                          //     ),
                                                          //     SizedBox(height: 4),
                                                          //     Row(
                                                          //       children: [
                                                          //         Text(
                                                          //           '• Last Modified: ${role.modifiedDate} by ${role.modifiedBy}',
                                                          //           style:
                                                          //               TextStyle(
                                                          //             color: Colors
                                                          //                 .white,
                                                          //             fontSize: 12,
                                                          //             fontWeight:
                                                          //                 FontWeight
                                                          //                     .w500,
                                                          //             fontFamily:
                                                          //                 'TiemposText',
                                                          //           ),
                                                          //         ),
                                                          //       ],
                                                          //     ),
                                                          //   ],
                                                          // ),
                                                        ],
                                                      ),
                                                    ),
                                                    //Profile Image and id,version
                                                    Row(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        // Profile image
                                                        Container(
                                                          margin:
                                                              EdgeInsets.all(
                                                                  AppSpacing
                                                                      .sm),
                                                          width: 80,
                                                          height: 85,
                                                          decoration:
                                                              BoxDecoration(
                                                            color: Colors
                                                                .grey.shade300,
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        4),
                                                          ),
                                                          child: ClipRRect(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        4),
                                                            child: Image.asset(
                                                              'assets/images/user_profile_image.png', // Path to your PNG image
                                                              fit: BoxFit.cover,
                                                            ),
                                                          ),
                                                        ),

                                                        // SizedBox(width: 10),

                                                        // Information container - more compact layout
                                                        Expanded(
                                                          child: Container(
                                                            //  width: 80,
                                                            height: 85,
                                                            margin: EdgeInsets.only(
                                                                top: AppSpacing
                                                                    .sm,
                                                                right:
                                                                    AppSpacing
                                                                        .sm,
                                                                bottom:
                                                                    AppSpacing
                                                                        .sm),
                                                            padding:
                                                                EdgeInsets.all(
                                                                    AppSpacing
                                                                        .sm),
                                                            decoration:
                                                                BoxDecoration(
                                                              color: Colors.grey
                                                                  .shade100,
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          4),
                                                              border:
                                                                  Border.all(
                                                                color: Colors
                                                                    .grey
                                                                    .shade300,
                                                                width: 1,
                                                              ),
                                                            ),
                                                            child: Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .min,
                                                              children: [
                                                                // First row: ID and Version side by side
                                                                Row(
                                                                  children: [
                                                                    Expanded(
                                                                      child: RichText(
                                                                          text: TextSpan(children: [
                                                                        TextSpan(
                                                                          text:
                                                                              'ID: ',
                                                                          style:
                                                                              TextStyle(
                                                                            fontSize:
                                                                                12,
                                                                            fontWeight:
                                                                                FontWeight.bold,
                                                                          ),
                                                                        ),
                                                                        TextSpan(
                                                                          text:
                                                                              'so008',
                                                                          style:
                                                                              TextStyle(
                                                                            fontSize:
                                                                                12,
                                                                            fontWeight:
                                                                                FontWeight.normal,
                                                                          ),
                                                                        ),
                                                                      ])),
                                                                    ),
                                                                    Expanded(
                                                                      child: RichText(
                                                                          text: TextSpan(children: [
                                                                        TextSpan(
                                                                          text:
                                                                              'Version: ',
                                                                          style:
                                                                              TextStyle(
                                                                            fontSize:
                                                                                12,
                                                                            fontWeight:
                                                                                FontWeight.bold,
                                                                          ),
                                                                        ),
                                                                        TextSpan(
                                                                          text:
                                                                              'V00019',
                                                                          style:
                                                                              TextStyle(
                                                                            fontSize:
                                                                                12,
                                                                            fontWeight:
                                                                                FontWeight.normal,
                                                                          ),
                                                                        ),
                                                                      ])),
                                                                    ),
                                                                  ],
                                                                ),

                                                                SizedBox(
                                                                    height:
                                                                        AppSpacing
                                                                            .xs),

                                                                // Second row: Created info
                                                                RichText(
                                                                    text: TextSpan(
                                                                        children: [
                                                                      TextSpan(
                                                                        text:
                                                                            'Created: ',
                                                                        style:
                                                                            TextStyle(
                                                                          fontSize:
                                                                              12,
                                                                          fontWeight:
                                                                              FontWeight.bold,
                                                                        ),
                                                                      ),
                                                                      TextSpan(
                                                                        text:
                                                                            '10/04/2025 by John Smith',
                                                                        style:
                                                                            TextStyle(
                                                                          fontSize:
                                                                              12,
                                                                          fontWeight:
                                                                              FontWeight.normal,
                                                                        ),
                                                                      ),
                                                                    ])),

                                                                SizedBox(
                                                                    height:
                                                                        AppSpacing
                                                                            .xs),

                                                                // Third row: Modified info
                                                                RichText(
                                                                    text: TextSpan(
                                                                        children: [
                                                                      TextSpan(
                                                                        text:
                                                                            'Modified: ',
                                                                        style:
                                                                            TextStyle(
                                                                          fontSize:
                                                                              12,
                                                                          fontWeight:
                                                                              FontWeight.bold,
                                                                        ),
                                                                      ),
                                                                      TextSpan(
                                                                        text:
                                                                            '28/04/2025 by Jane Doe',
                                                                        style:
                                                                            TextStyle(
                                                                          fontSize:
                                                                              12,
                                                                          fontWeight:
                                                                              FontWeight.normal,
                                                                        ),
                                                                      ),
                                                                    ])),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    // Editable Properties section
                                                    Container(
                                                      margin: EdgeInsets.only(
                                                        left: 12,
                                                        right: 12,
                                                        bottom: 12,
                                                      ),
                                                      padding:
                                                          EdgeInsets.all(12),
                                                      decoration: BoxDecoration(
                                                        color: Colors.white,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(4),
                                                        border: Border.all(
                                                          color:
                                                              Color(0xff1890FF),
                                                          width: 1,
                                                        ),
                                                      ),
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          RichText(
                                                            text: TextSpan(
                                                              children: [
                                                                TextSpan(
                                                                  text:
                                                                      'CRM Administrator: ',
                                                                  style:
                                                                      TextStyle(
                                                                    fontSize:
                                                                        12,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                    color: Colors
                                                                        .black,
                                                                    height: 1.5,
                                                                  ),
                                                                ),
                                                                TextSpan(
                                                                  text:
                                                                      'Can access Claims Processing, Claims Investigation and Claims Recovery',
                                                                  style:
                                                                      TextStyle(
                                                                    fontSize:
                                                                        12,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .normal,
                                                                    color: Colors
                                                                        .black,
                                                                    height: 1.5,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),

                                                          // Text(
                                                          //   'CRM Administrator:',
                                                          //   style: TextStyle(
                                                          //     fontWeight:
                                                          //         FontWeight.bold,
                                                          //     fontSize: 12,
                                                          //     fontFamily:
                                                          //         'TiemposText',
                                                          //   ),
                                                          // ),
                                                          SizedBox(height: 8),
                                                          // if (role.useCases !=
                                                          //     null)
                                                          //   ...role.useCases!
                                                          //       .asMap()
                                                          //       .entries
                                                          //       .map(
                                                          //     (entry) {
                                                          //       final index =
                                                          //           entry.key;
                                                          //       final useCase =
                                                          //           entry.value;

                                                          //       return Padding(
                                                          //         padding: EdgeInsets
                                                          //             .only(
                                                          //                 left:
                                                          //                     12,
                                                          //                 top: 4),
                                                          //         child: index ==
                                                          //                 0
                                                          //             ? RichText(
                                                          //                 text:
                                                          //                     TextSpan(
                                                          //                   children: [
                                                          //                     TextSpan(
                                                          //                       text: '• Use Cases: ',
                                                          //                       style: TextStyle(
                                                          //                         fontWeight: FontWeight.bold,
                                                          //                         fontSize: 13,
                                                          //                         fontFamily: 'TiemposText',
                                                          //                         color: Colors.black,
                                                          //                       ),
                                                          //                     ),
                                                          //                     TextSpan(
                                                          //                       text: useCase,
                                                          //                       style: TextStyle(
                                                          //                         fontSize: 13,
                                                          //                         fontFamily: 'TiemposText',
                                                          //                         color: Colors.black,
                                                          //                       ),
                                                          //                     ),
                                                          //                   ],
                                                          //                 ),
                                                          //               )
                                                          //             : Text(
                                                          //                 useCase,
                                                          //                 style:
                                                          //                     TextStyle(
                                                          //                   fontSize:
                                                          //                       13,
                                                          //                   fontFamily:
                                                          //                       'TiemposText',
                                                          //                 ),
                                                          //               ),
                                                          //       );
                                                          //     },
                                                          //   ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            preferBelow: true,
                                            verticalOffset: 20,
                                            padding: EdgeInsets.zero,
                                            margin: EdgeInsets.zero,
                                            showDuration: Duration(seconds: 10),
                                            decoration: BoxDecoration(
                                              color: Colors.transparent,
                                              boxShadow: [],
                                            ),
                                            child: Text(
                                              "${role.title} ",
                                              style: TextStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.bold,
                                                fontFamily: 'TiemposText',
                                                color: Colors.black,
                                                // decoration: TextDecoration.underline,
                                                decorationColor:
                                                    Colors.blue.shade700,
                                                decorationThickness: 1,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      // WidgetSpan(
                                      //   child: MouseRegion(
                                      //     child: Tooltip(
                                      //       // Your existing tooltip implementation
                                      //       richMessage: WidgetSpan(
                                      //         child: Container(
                                      //           margin: EdgeInsets.only(
                                      //               left: 200, top: 10),
                                      //           width: 350,
                                      //           decoration: BoxDecoration(
                                      //             color: Colors.white,
                                      //             borderRadius:
                                      //                 BorderRadius.circular(8),
                                      //             boxShadow: [
                                      //               BoxShadow(
                                      //                 color: Colors.black
                                      //                     .withValues(
                                      //                         alpha: 0.2),
                                      //                 blurRadius: 10,
                                      //                 offset: Offset(0, 3),
                                      //               ),
                                      //             ],
                                      //           ),
                                      //           child: Column(
                                      //             mainAxisSize:
                                      //                 MainAxisSize.min,
                                      //             crossAxisAlignment:
                                      //                 CrossAxisAlignment.start,
                                      //             children: [
                                      //               // Blue header with icon
                                      //               Container(
                                      //                 padding:
                                      //                     EdgeInsets.all(12),
                                      //                 decoration: BoxDecoration(
                                      //                   color: Colors
                                      //                       .blue.shade700,
                                      //                   borderRadius:
                                      //                       BorderRadius.only(
                                      //                     topLeft:
                                      //                         Radius.circular(
                                      //                             8),
                                      //                     topRight:
                                      //                         Radius.circular(
                                      //                             8),
                                      //                   ),
                                      //                 ),
                                      //                 child: Row(
                                      //                   children: [
                                      //                     Container(
                                      //                       width: 40,
                                      //                       height: 40,
                                      //                       decoration:
                                      //                           BoxDecoration(
                                      //                         color:
                                      //                             Colors.white,
                                      //                         shape: BoxShape
                                      //                             .circle,
                                      //                       ),
                                      //                       child: Icon(
                                      //                         Icons
                                      //                             .person_outline,
                                      //                         color: Colors.blue
                                      //                             .shade700,
                                      //                         size: 24,
                                      //                       ),
                                      //                     ),
                                      //                     SizedBox(width: 12),
                                      //                     Column(
                                      //                       crossAxisAlignment:
                                      //                           CrossAxisAlignment
                                      //                               .start,
                                      //                       children: [
                                      //                         Row(
                                      //                           children: [
                                      //                             Text(
                                      //                               '• ID: ${role.id}',
                                      //                               style:
                                      //                                   TextStyle(
                                      //                                 color: Colors
                                      //                                     .white,
                                      //                                 fontSize:
                                      //                                     12,
                                      //                                 fontWeight:
                                      //                                     FontWeight
                                      //                                         .w500,
                                      //                               ),
                                      //                             ),
                                      //                             SizedBox(
                                      //                                 width: 8),
                                      //                             Text(
                                      //                               '• Version: ${role.version}',
                                      //                               style:
                                      //                                   TextStyle(
                                      //                                 color: Colors
                                      //                                     .white,
                                      //                                 fontSize:
                                      //                                     12,
                                      //                                 fontWeight:
                                      //                                     FontWeight
                                      //                                         .w500,
                                      //                               ),
                                      //                             ),
                                      //                           ],
                                      //                         ),
                                      //                         SizedBox(
                                      //                             height: 4),
                                      //                         Row(
                                      //                           children: [
                                      //                             Text(
                                      //                               '• Created: ${role.createdDate} by ${role.createdBy}',
                                      //                               style:
                                      //                                   TextStyle(
                                      //                                 color: Colors
                                      //                                     .white,
                                      //                                 fontSize:
                                      //                                     12,
                                      //                                 fontWeight:
                                      //                                     FontWeight
                                      //                                         .w500,
                                      //                               ),
                                      //                             ),
                                      //                           ],
                                      //                         ),
                                      //                         SizedBox(
                                      //                             height: 4),
                                      //                         Row(
                                      //                           children: [
                                      //                             Text(
                                      //                               '• Last Modified: ${role.modifiedDate} by ${role.modifiedBy}',
                                      //                               style:
                                      //                                   TextStyle(
                                      //                                 color: Colors
                                      //                                     .white,
                                      //                                 fontSize:
                                      //                                     12,
                                      //                                 fontWeight:
                                      //                                     FontWeight
                                      //                                         .w500,
                                      //                               ),
                                      //                             ),
                                      //                           ],
                                      //                         ),
                                      //                       ],
                                      //                     ),
                                      //                   ],
                                      //                 ),
                                      //               ),
                                      //               // Editable Properties section
                                      //               Container(
                                      //                 padding:
                                      //                     EdgeInsets.all(12),
                                      //                 child: Column(
                                      //                   crossAxisAlignment:
                                      //                       CrossAxisAlignment
                                      //                           .start,
                                      //                   children: [
                                      //                     Text(
                                      //                       'Editable Properties:',
                                      //                       style: TextStyle(
                                      //                         fontWeight:
                                      //                             FontWeight
                                      //                                 .bold,
                                      //                         fontSize: 14,
                                      //                       ),
                                      //                     ),
                                      //                     SizedBox(height: 8),
                                      //                     Text(
                                      //                       '• Use Cases:',
                                      //                       style: TextStyle(
                                      //                         fontWeight:
                                      //                             FontWeight
                                      //                                 .w500,
                                      //                         fontSize: 13,
                                      //                       ),
                                      //                     ),
                                      //                     if (role.useCases !=
                                      //                         null)
                                      //                       ...role.useCases!
                                      //                           .map(
                                      //                               (useCase) =>
                                      //                                   Padding(
                                      //                                     padding: EdgeInsets.only(
                                      //                                         left: 12,
                                      //                                         top: 4),
                                      //                                     child:
                                      //                                         Text(
                                      //                                       useCase,
                                      //                                       style:
                                      //                                           TextStyle(fontSize: 13),
                                      //                                     ),
                                      //                                   )),
                                      //                   ],
                                      //                 ),
                                      //               ),
                                      //             ],
                                      //           ),
                                      //         ),
                                      //       ),
                                      //       preferBelow: true,
                                      //       verticalOffset: 0,
                                      //       padding: EdgeInsets.zero,
                                      //       margin: EdgeInsets.zero,
                                      //       showDuration: Duration(seconds: 10),
                                      //       decoration: BoxDecoration(
                                      //         color: Colors.transparent,
                                      //         boxShadow: [],
                                      //       ),
                                      //       child: Text(
                                      //         "${role.title} ",
                                      //         style: TextStyle(
                                      //           fontSize: 14,
                                      //           fontWeight: FontWeight.bold,
                                      //           color: Colors.black,
                                      //           fontFamily: "TiemposText",
                                      //         ),
                                      //       ),
                                      //     ),
                                      //   ),
                                      // ),
                                      // All use cases as a single text without line break
                                      // Replace the single TextSpan with individual clickable spans
                                      // Replace the TextSpan with a GestureDetector wrapped TextSpan

// Replace the single TextSpan with individual clickable spans
                                      ...role.useCases!
                                          .map((useCase) => WidgetSpan(
                                                child: GestureDetector(
                                                  onTap: () {
                                                    // final RenderBox renderBox =
                                                    //     context.findRenderObject()
                                                    //         as RenderBox;
                                                    // final position =
                                                    //     renderBox.localToGlobal(
                                                    //         Offset.zero);
                                                    // Show tooltip with chat field when clicked

                                                    showDialog(
                                                      context: context,
                                                      barrierColor:
                                                          Colors.transparent,
                                                      builder: (BuildContext
                                                          context) {
                                                        return Stack(
                                                          children: [
                                                            Positioned(
                                                              right: 30,
                                                              // position.dx,
                                                              top: 130,
                                                              //position.dy ,
                                                              // + 20,
                                                              child: Dialog(
                                                                backgroundColor:
                                                                    Colors
                                                                        .white,
                                                                elevation: 8,
                                                                shape:
                                                                    RoundedRectangleBorder(
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              12),
                                                                ),
                                                                insetPadding:
                                                                    EdgeInsets
                                                                        .zero,

                                                                // bottom: 50,
                                                                // ),
                                                                child:
                                                                    Container(
                                                                  margin: EdgeInsets
                                                                      .symmetric(
                                                                          // horizontal: AppSpacing.md,
                                                                          // vertical: AppSpacing.md,
                                                                          ),
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: Colors
                                                                        .white,
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            AppSpacing.md),
                                                                    border: Border.all(
                                                                        color: Color.fromARGB(
                                                                            255,
                                                                            94,
                                                                            162,
                                                                            192),
                                                                        width:
                                                                            1),
                                                                    boxShadow: [],
                                                                  ),
                                                                  constraints:
                                                                      BoxConstraints(
                                                                    maxHeight:
                                                                        300,
                                                                    minHeight:
                                                                        200,
                                                                  ),
                                                                  width: MediaQuery.of(
                                                                              context)
                                                                          .size
                                                                          .width /
                                                                      3.8,
                                                                  padding: EdgeInsets.all(
                                                                      AppSpacing
                                                                          .xs),
                                                                  child: Column(
                                                                    mainAxisSize:
                                                                        MainAxisSize
                                                                            .min,
                                                                    children: [
                                                                      Flexible(
                                                                        child:
                                                                            TextField(
                                                                          controller:
                                                                              chatController,
                                                                          maxLines:
                                                                              null,
                                                                          decoration:
                                                                              InputDecoration(
                                                                            focusColor:
                                                                                Colors.transparent,
                                                                            hintText:
                                                                                "You want to make a comment on ${role.title} use cases? Please put your points in details I will help you to integrate it.",
                                                                            hintStyle:
                                                                                TextStyle(
                                                                              fontSize: 14,
                                                                              fontWeight: FontWeight.normal,
                                                                              color: Colors.grey,
                                                                              fontFamily: "TiemposText",
                                                                            ),
                                                                            hoverColor:
                                                                                Colors.transparent,
                                                                            border:
                                                                                OutlineInputBorder(borderSide: BorderSide.none),
                                                                            enabledBorder:
                                                                                OutlineInputBorder(borderSide: BorderSide.none),
                                                                            focusedBorder:
                                                                                OutlineInputBorder(borderSide: BorderSide.none),
                                                                            errorBorder:
                                                                                OutlineInputBorder(borderSide: BorderSide.none),
                                                                            disabledBorder:
                                                                                OutlineInputBorder(borderSide: BorderSide.none),
                                                                          ),
                                                                          onSubmitted: (_) =>
                                                                              _sendMessage(),
                                                                        ),
                                                                      ),
                                                                      SizedBox(
                                                                          height:
                                                                              40),
                                                                      Row(
                                                                        mainAxisAlignment:
                                                                            MainAxisAlignment.end,
                                                                        children: [
                                                                          hoverButtons(
                                                                              icon: Icon(Icons.mic_rounded),
                                                                              onPressed: () {}),
                                                                          hoverButtons(
                                                                              icon: Icon(Icons.attachment),
                                                                              onPressed: () {}),
                                                                          hoverButtons(
                                                                              icon: Icon(Icons.add),
                                                                              onPressed: () {}),
                                                                          hoverButtons(
                                                                            icon:
                                                                                Icon(Icons.arrow_upward),
                                                                            onPressed:
                                                                                _sendMessage,
                                                                          ),
                                                                        ],
                                                                      )
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        );
                                                      },
                                                    );
                                                  },
                                                  child: Text(
                                                    "$useCase, ",
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      fontFamily: "TiemposText",
                                                      color: Colors.black,
                                                    ),
                                                  ),
                                                ),
                                              )),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),

                        // Permissions section - keep your existing implementation
                        Container(
                          key: roleSectionKeys['permissions'],
                          padding: EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Permissions',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                  fontFamily: "TiemposText",
                                ),
                              ),
                              SizedBox(height: 8),
                              RichText(
                                text: TextSpan(
                                  children: [
                                    TextSpan(
                                      text: role.title,
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight
                                            .bold, // Bold for role title
                                        color: Colors.black,
                                        fontFamily: "TiemposText",
                                      ),
                                    ),
                                    TextSpan(
                                      text: ' has the following permissions:',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight
                                            .normal, // Normal for the rest of text
                                        color: Colors.black,
                                        fontFamily: "TiemposText",
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              // MouseRegion(
                              //   // cursor: SystemMouseCursors.click,
                              //   child: Tooltip(
                              //     richMessage: WidgetSpan(
                              //       child: Container(
                              //         margin:
                              //             EdgeInsets.only(left: 10, top: 10),
                              //         width: 350,
                              //         decoration: BoxDecoration(
                              //           color: Colors.white,
                              //           borderRadius: BorderRadius.circular(8),
                              //           boxShadow: [
                              //             BoxShadow(
                              //               color: Colors.black.withValues(
                              //                   alpha: 51), // 0.2 * 255 = ~51
                              //               blurRadius: 10,
                              //               offset: Offset(0, 3),
                              //             ),
                              //           ],
                              //         ),
                              //         child: Column(
                              //           mainAxisSize: MainAxisSize.min,
                              //           crossAxisAlignment:
                              //               CrossAxisAlignment.start,
                              //           children: [
                              //             // Blue header with icon
                              //             Container(
                              //               padding: EdgeInsets.all(12),
                              //               decoration: BoxDecoration(
                              //                 color: Colors.blue.shade700,
                              //                 borderRadius: BorderRadius.only(
                              //                   topLeft: Radius.circular(8),
                              //                   topRight: Radius.circular(8),
                              //                 ),
                              //               ),
                              //               child: Row(
                              //                 children: [
                              //                   Container(
                              //                     width: 40,
                              //                     height: 40,
                              //                     decoration: BoxDecoration(
                              //                       color: Colors.white,
                              //                       shape: BoxShape.circle,
                              //                     ),
                              //                     child: Icon(
                              //                       Icons.person_outline,
                              //                       color: Colors.blue.shade700,
                              //                       size: 24,
                              //                     ),
                              //                   ),
                              //                   SizedBox(width: 12),
                              //                   Column(
                              //                     crossAxisAlignment:
                              //                         CrossAxisAlignment.start,
                              //                     children: [
                              //                       Row(
                              //                         children: [
                              //                           Text(
                              //                             '• ID: ${role.id}',
                              //                             style: TextStyle(
                              //                               color: Colors.white,
                              //                               fontSize: 12,
                              //                               fontWeight:
                              //                                   FontWeight.w500,
                              //                             ),
                              //                           ),
                              //                           SizedBox(width: 8),
                              //                           Text(
                              //                             '• Version: ${role.version}',
                              //                             style: TextStyle(
                              //                               color: Colors.white,
                              //                               fontSize: 12,
                              //                               fontWeight:
                              //                                   FontWeight.w500,
                              //                             ),
                              //                           ),
                              //                         ],
                              //                       ),
                              //                       SizedBox(height: 4),
                              //                       Row(
                              //                         children: [
                              //                           Text(
                              //                             '• Created: ${role.createdDate} by ${role.createdBy}',
                              //                             style: TextStyle(
                              //                               color: Colors.white,
                              //                               fontSize: 12,
                              //                               fontWeight:
                              //                                   FontWeight.w500,
                              //                             ),
                              //                           ),
                              //                         ],
                              //                       ),
                              //                       SizedBox(height: 4),
                              //                       Row(
                              //                         children: [
                              //                           Text(
                              //                             '• Last Modified: ${role.modifiedDate} by ${role.modifiedBy}',
                              //                             style: TextStyle(
                              //                               color: Colors.white,
                              //                               fontSize: 12,
                              //                               fontWeight:
                              //                                   FontWeight.w500,
                              //                             ),
                              //                           ),
                              //                         ],
                              //                       ),
                              //                     ],
                              //                   ),
                              //                 ],
                              //               ),
                              //             ),
                              //             // Editable Properties section
                              //             Container(
                              //               padding: EdgeInsets.all(12),
                              //               child: Column(
                              //                 crossAxisAlignment:
                              //                     CrossAxisAlignment.start,
                              //                 children: [
                              //                   Text(
                              //                     'Editable Properties:',
                              //                     style: TextStyle(
                              //                       fontWeight: FontWeight.bold,
                              //                       fontSize: 14,
                              //                     ),
                              //                   ),
                              //                   SizedBox(height: 8),
                              //                   Text(
                              //                     '• Use Cases:',
                              //                     style: TextStyle(
                              //                       fontWeight: FontWeight.w500,
                              //                       fontSize: 13,
                              //                     ),
                              //                   ),
                              //                   if (role.useCases != null)
                              //                     ...role.useCases!
                              //                         .map((useCase) => Padding(
                              //                               padding:
                              //                                   EdgeInsets.only(
                              //                                       left: 12,
                              //                                       top: 4),
                              //                               child: Text(
                              //                                 useCase,
                              //                                 style: TextStyle(
                              //                                     fontSize: 13),
                              //                               ),
                              //                             )),
                              //                 ],
                              //               ),
                              //             ),
                              //           ],
                              //         ),
                              //       ),
                              //     ),
                              //     preferBelow: true,
                              //     verticalOffset: 0,
                              //     padding: EdgeInsets.zero,
                              //     margin: EdgeInsets.zero,
                              //     showDuration: Duration(seconds: 10),
                              //     decoration: BoxDecoration(
                              //       color: Colors.transparent,
                              //       boxShadow: [],
                              //     ),
                              //     child: RichText(
                              //       text: TextSpan(
                              //         children: [
                              //           TextSpan(
                              //             text: role.title,
                              //             style: TextStyle(
                              //               fontSize: 14,
                              //               fontWeight: FontWeight
                              //                   .bold, // Bold for role title
                              //               color: Colors.black,
                              //               fontFamily: "TiemposText",
                              //             ),
                              //           ),
                              //           TextSpan(
                              //             text:
                              //                 ' has the following permissions:',
                              //             style: TextStyle(
                              //               fontSize: 14,
                              //               fontWeight: FontWeight
                              //                   .normal, // Normal for the rest of text
                              //               color: Colors.black,
                              //               fontFamily: "TiemposText",
                              //             ),
                              //           ),
                              //         ],
                              //       ),
                              //     ),
                              //   ),
                              // ),

                              SizedBox(height: 12),

                              // Entity permissions
                              ...(role.permissions?['entities'] ??
                                      [
                                        'Can Read entity Policy',
                                        'Can Read entity Customer',
                                        'Can Read, Create, Update, Delete entity Claim',
                                        'Can Read, Create, Update, Delete entity ClaimNote',
                                        'Can Read, Create, Update, Delete entity ClaimDocument',
                                        'Can Read, Create, Update, Delete entity ClaimPayment',
                                        'Can Read entity Coverage',
                                      ])
                                  .map((permission) => Padding(
                                        padding: EdgeInsets.only(
                                            // bottom: 2,
                                            left: 8),
                                        child: Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text('• ',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  // height: 0.5,
                                                )),
                                            Expanded(
                                              child: GestureDetector(
                                                onTap: () {
                                                  // Get the render box of the text
                                                  // final RenderBox textBox =
                                                  //     context.findRenderObject()
                                                  //         as RenderBox;
                                                  // Get the global position of the text
                                                  // final Offset textPosition =
                                                  //     textBox.localToGlobal(
                                                  //         Offset.zero);

                                                  // Show dialog
                                                  showDialog(
                                                    context: context,
                                                    barrierColor:
                                                        Colors.transparent,
                                                    builder:
                                                        (BuildContext context) {
                                                      return Stack(
                                                        children: [
                                                          Positioned(
                                                            right: 30,
                                                            top: 130,
                                                            child: Dialog(
                                                              backgroundColor:
                                                                  Colors.white,
                                                              elevation: 8,
                                                              shape:
                                                                  RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            12),
                                                              ),
                                                              insetPadding:
                                                                  EdgeInsets
                                                                      .zero,
                                                              child: Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                  border: Border.all(
                                                                      color: const Color
                                                                          .fromARGB(
                                                                          255,
                                                                          94,
                                                                          162,
                                                                          192)),
                                                                  borderRadius:
                                                                      BorderRadius.circular(
                                                                          AppSpacing
                                                                              .md),
                                                                ),
                                                                constraints:
                                                                    BoxConstraints(
                                                                  maxHeight:
                                                                      300,
                                                                  minHeight:
                                                                      200,
                                                                ),
                                                                width: MediaQuery.of(
                                                                            context)
                                                                        .size
                                                                        .width /
                                                                    3.8,
                                                                padding:
                                                                    EdgeInsets.all(
                                                                        AppSpacing
                                                                            .xs),
                                                                child: Column(
                                                                  mainAxisSize:
                                                                      MainAxisSize
                                                                          .min,
                                                                  children: [
                                                                    Flexible(
                                                                      child:
                                                                          TextField(
                                                                        controller:
                                                                            chatController,
                                                                        maxLines:
                                                                            null,
                                                                        decoration:
                                                                            InputDecoration(
                                                                          focusColor:
                                                                              Colors.transparent,
                                                                          hintText:
                                                                              "You want to make a comment on ${role.title} permissions? Please put your points in details I will help you to integrate it.",
                                                                          hintStyle:
                                                                              TextStyle(
                                                                            fontSize:
                                                                                14,
                                                                            fontWeight:
                                                                                FontWeight.normal,
                                                                            color:
                                                                                Colors.grey,
                                                                            fontFamily:
                                                                                "TiemposText",
                                                                          ),
                                                                          hoverColor:
                                                                              Colors.transparent,
                                                                          border:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                          enabledBorder:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                          focusedBorder:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                          errorBorder:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                          disabledBorder:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                        ),
                                                                        onSubmitted:
                                                                            (_) =>
                                                                                _sendMessage(),
                                                                      ),
                                                                    ),
                                                                    SizedBox(
                                                                        height:
                                                                            40),
                                                                    Row(
                                                                      mainAxisAlignment:
                                                                          MainAxisAlignment
                                                                              .end,
                                                                      children: [
                                                                        hoverButtons(
                                                                            icon:
                                                                                Icon(Icons.mic_rounded),
                                                                            onPressed: () {}),
                                                                        hoverButtons(
                                                                            icon:
                                                                                Icon(Icons.attachment),
                                                                            onPressed: () {}),
                                                                        hoverButtons(
                                                                            icon:
                                                                                Icon(Icons.add),
                                                                            onPressed: () {}),
                                                                        hoverButtons(
                                                                          icon:
                                                                              Icon(Icons.arrow_upward),
                                                                          onPressed:
                                                                              _sendMessage,
                                                                        ),
                                                                      ],
                                                                    )
                                                                  ],
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      );
                                                    },
                                                  );
                                                },
                                                child: Text(
                                                  permission,
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    fontFamily: "TiemposText",
                                                    height: 1.5,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      )),

                              SizedBox(height: 12),

                              // Objectives
                              Padding(
                                padding: const EdgeInsets.only(left: 7.0),
                                child: Text(
                                  '• Can Execute objectives:',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: "TiemposText",
                                  ),
                                ),
                              ),
                              SizedBox(height: 2),

                              ...(role.permissions?['objectives'] ??
                                      [
                                        'Create Claim',
                                        'Assign Claim',
                                        'Review Claim',
                                        'Approve Claim',
                                        'Reject Claim',
                                        'Close Claim',
                                        'Initiate Investigation',
                                        'Assign Investigator',
                                        'Collect Evidence',
                                      ])
                                  .map((objective) => Padding(
                                        padding: EdgeInsets.only(
                                            // bottom: 2,
                                            left: 12),
                                        child: Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text('- ',
                                                style: TextStyle(
                                                    color:
                                                        Colors.grey.shade600)),
                                            Expanded(
                                              child: GestureDetector(
                                                onTap: () {
                                                  // Get the render box of the text
                                                  // final RenderBox textBox =
                                                  //     context.findRenderObject()
                                                  //         as RenderBox;
                                                  // // Get the global position of the text
                                                  // final Offset textPosition =
                                                  //     textBox.localToGlobal(
                                                  //         Offset.zero);

                                                  // Show dialog
                                                  showDialog(
                                                    context: context,
                                                    barrierColor:
                                                        Colors.transparent,
                                                    builder:
                                                        (BuildContext context) {
                                                      return Stack(
                                                        children: [
                                                          Positioned(
                                                            right: 30,
                                                            top: 130,
                                                            child: Dialog(
                                                              backgroundColor:
                                                                  Colors.white,
                                                              elevation: 8,
                                                              shape:
                                                                  RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            12),
                                                              ),
                                                              insetPadding:
                                                                  EdgeInsets
                                                                      .zero,
                                                              child: Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                  border: Border.all(
                                                                      color: const Color
                                                                          .fromARGB(
                                                                          255,
                                                                          94,
                                                                          162,
                                                                          192)),
                                                                  borderRadius:
                                                                      BorderRadius.circular(
                                                                          AppSpacing
                                                                              .md),
                                                                ),
                                                                constraints:
                                                                    BoxConstraints(
                                                                  maxHeight:
                                                                      300,
                                                                  minHeight:
                                                                      200,
                                                                ),
                                                                width: MediaQuery.of(
                                                                            context)
                                                                        .size
                                                                        .width /
                                                                    3.8,
                                                                padding:
                                                                    EdgeInsets.all(
                                                                        AppSpacing
                                                                            .xs),
                                                                child: Column(
                                                                  mainAxisSize:
                                                                      MainAxisSize
                                                                          .min,
                                                                  children: [
                                                                    Flexible(
                                                                      child:
                                                                          TextField(
                                                                        controller:
                                                                            chatController,
                                                                        maxLines:
                                                                            null,
                                                                        decoration:
                                                                            InputDecoration(
                                                                          focusColor:
                                                                              Colors.transparent,
                                                                          hintText:
                                                                              "You want to make a comment on ${role.title} objectives? Please put your points in details I will help you to integrate it.",
                                                                          hintStyle:
                                                                              TextStyle(
                                                                            fontSize:
                                                                                14,
                                                                            fontWeight:
                                                                                FontWeight.normal,
                                                                            color:
                                                                                Colors.grey,
                                                                            fontFamily:
                                                                                "TiemposText",
                                                                          ),
                                                                          hoverColor:
                                                                              Colors.transparent,
                                                                          border:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                          enabledBorder:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                          focusedBorder:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                          errorBorder:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                          disabledBorder:
                                                                              OutlineInputBorder(borderSide: BorderSide.none),
                                                                        ),
                                                                        onSubmitted:
                                                                            (_) =>
                                                                                _sendMessage(),
                                                                      ),
                                                                    ),
                                                                    SizedBox(
                                                                        height:
                                                                            40),
                                                                    Row(
                                                                      mainAxisAlignment:
                                                                          MainAxisAlignment
                                                                              .end,
                                                                      children: [
                                                                        hoverButtons(
                                                                            icon:
                                                                                Icon(Icons.mic_rounded),
                                                                            onPressed: () {}),
                                                                        hoverButtons(
                                                                            icon:
                                                                                Icon(Icons.attachment),
                                                                            onPressed: () {}),
                                                                        hoverButtons(
                                                                            icon:
                                                                                Icon(Icons.add),
                                                                            onPressed: () {}),
                                                                        hoverButtons(
                                                                          icon:
                                                                              Icon(Icons.arrow_upward),
                                                                          onPressed:
                                                                              _sendMessage,
                                                                        ),
                                                                      ],
                                                                    )
                                                                  ],
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      );
                                                    },
                                                  );
                                                },
                                                child: Text(
                                                  objective,
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    fontFamily: "TiemposText",
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      )),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ]));
  }

  // // Build side panel for entity details
  Widget _buildEntityDetailsPanel(Entity entity) {
    // Define the sections for navigation
    final List<String> sections = [
      'Attributes',
      'Business Rules',
      'Relationships',
      'Circuit Board',
      'Constants & Validations',
    ];

    // Create a scroll controller to handle scrolling to sections
    final ScrollController scrollController = ScrollController();

    // Define fixed scroll positions for each section
    // These values are based on approximate section heights
    final List<double> sectionOffsets = [0.0, 450.0, 650.0, 850.0, 1200.0];

    // Function to scroll to a specific section
    void scrollToSection(int index) {
      if (index < 0 || index >= sections.length) return;

      // Get the target position from our predefined offsets
      final targetPosition = sectionOffsets[index];

      // Update the selected section index
      setState(() {
        selectedSectionIndex = index;
      });

      // Animate to the position
      scrollController.animateTo(
        targetPosition,
        duration: Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left navigation sidebar
          // Main content area
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with entity title and close button
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade300, width: 1),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            CircleAvatar(
                              backgroundColor:
                                  const Color.fromARGB(255, 92, 163, 234),
                              radius: 10,
                              child: Icon(
                                Icons.person_outline,
                                color: Colors.white,
                                size: 12,
                              ),
                            ),
                            SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                entity.title ?? '',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  fontFamily: "TiemposText",
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: Icon(Icons.chat_outlined),
                        padding: EdgeInsets.zero,
                        constraints: BoxConstraints(),
                        onPressed: () {
                          setState(() {
                            showSidePanel = false;
                          });
                        },
                      ),
                    ],
                  ),
                ), // Scrollable content
                Expanded(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 45,
                        decoration: BoxDecoration(
                          color: Colors.white,
                        ),
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: sections.length,
                          itemBuilder: (context, index) {
                            return MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: GestureDetector(
                                onTap: () => scrollToSection(index),
                                child: Container(
                                  padding: EdgeInsets.symmetric(vertical: 2),
                                  margin: EdgeInsets.symmetric(vertical: 1),
                                  // decoration: BoxDecoration(
                                  //   color: selectedSectionIndex == index
                                  //       ? Colors.blue.shade50
                                  //       : Colors.transparent,
                                  //   borderRadius: BorderRadius.circular(4),
                                  // ),
                                  child: Center(
                                    child: Column(
                                      children: [
                                        Text(
                                          _getSectionAbbreviation(
                                              sections[index]),
                                          style: TextStyle(
                                            fontFamily: 'TiemposText',
                                            color: selectedSectionIndex == index
                                                ? Colors.black
                                                : Colors.grey.shade800,
                                            fontWeight:
                                                selectedSectionIndex == index
                                                    ? FontWeight.bold
                                                    : FontWeight.w500,
                                            fontSize: 9,
                                          ),
                                        ),
                                        SizedBox(height: 1),
                                        Container(
                                          height: 1,
                                          width: 16,
                                          color: selectedSectionIndex == index
                                              ? Colors.black
                                              : Colors.grey.shade400,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      Expanded(
                        child: SingleChildScrollView(
                          controller: scrollController,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Attributes section
                              _buildSectionHeader('Attributes'),
                              _buildAttributesSection(entity),

                              // Business Rules section
                              _buildSectionHeader('Business Rules'),
                              _buildBusinessRulesSection(entity),

                              // Relationships section
                              _buildSectionHeader('Relationships'),
                              _buildRelationshipsSection(),

                              // Circuit Board section
                              _buildSectionHeader('Circuit Board'),
                              _buildCircuitBoardSection(),

                              // Constants & Validations section
                              _buildSectionHeader('Constants & Validations'),
                              _buildConstantsAndValidationsSection(),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build section headers
  Widget _buildSectionHeader(String title, {Key? key}) {
    return Container(
      key: key,
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(color: Colors.white),
      child: Row(
        children: [
          Text(
            title,
            style: TextStyle(
              fontFamily: 'TiemposText',
              fontWeight: FontWeight.bold,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build attributes section
  Widget _buildAttributesSection(Entity entity) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 3),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Customer ID
          _buildAttributeDetail(
            'Customer ID',
            'Data type is String (alphanumeric with maximum 100 characters). Error message: "Customer ID can only contain letters, numbers and spaces." Mandatory field.',
          ),

          // Name
          _buildAttributeDetail(
            'Name',
            'Data Type is String (100 characters). Error message: "Name cannot exceed 100 characters." Mandatory field.',
          ),

          // Email
          _buildAttributeDetail(
            'Email',
            'Data Type is String (Must follow valid email format). Error message: "Please enter a valid email address." Mandatory field.',
          ),

          // Phone Number
          _buildAttributeDetail(
            'Phone Number',
            'Data Type is String, Mandatory field. Must contain only digits and symbols + - ( ). Error message: "Phone number format is incorrect."',
          ),

          // Address
          _buildAttributeDetail(
            'Address',
            'Data Type is String, Optional No specific validation rules.',
          ),

          // Gender
          _buildAttributeDetail(
            'Gender',
            'Data Type is String, Optional Must be one of: Male, Female, Other, Prefer not to say. Error message: "Please select a valid gender option."',
          ),
        ],
      ),
    );
  }

  // Helper method to build a single attribute detail
  Widget _buildAttributeDetail(String name, String description) {
    // Extract data type for the tooltip
    String dataType = "String";

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.only(bottom: 8),
          child: RichText(
            text: TextSpan(
              style: TextStyle(
                height: 1.4, // Increased line height for better readability
              ),
              children: [
                WidgetSpan(
                  alignment: PlaceholderAlignment.middle,
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: Tooltip(
                      richMessage: WidgetSpan(
                        child: Container(
                          margin: EdgeInsets.only(left: 230),
                          width: 300,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black
                                    .withValues(alpha: 51), // 0.2 * 255 = ~51
                                blurRadius: 10,
                                offset: Offset(0, 3),
                              ),
                            ],
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Blue header with icon
                              Container(
                                padding: EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.blue.shade700,
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(8),
                                    topRight: Radius.circular(8),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 40,
                                      height: 40,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        Icons.folder,
                                        color: Colors.blue.shade700,
                                        size: 24,
                                      ),
                                    ),
                                    SizedBox(width: 12),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          name,
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              // Attribute Properties section
                              Container(
                                padding: EdgeInsets.all(12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Text(
                                          '• ID : at001',
                                          style: TextStyle(
                                            fontSize: 11,
                                          ),
                                        ),
                                        SizedBox(width: 4),
                                        Text(
                                          '• Version :V00012',
                                          style: TextStyle(
                                            fontSize: 11,
                                          ),
                                        ),
                                        SizedBox(width: 4),
                                        Text(
                                          '• Data Type: $dataType',
                                          style: TextStyle(
                                            fontSize: 11,
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      '• Created : 12/04/2025 by John Smith',
                                      style: TextStyle(
                                        fontSize: 11,
                                      ),
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      '• Last Modified : 12/04/2025 by John Smith',
                                      style: TextStyle(
                                        fontSize: 11,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      preferBelow: false,
                      verticalOffset: 20,
                      padding: EdgeInsets.zero,
                      margin: EdgeInsets.zero,
                      showDuration: Duration(seconds: 10),
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        boxShadow: [],
                      ),
                      child: Text(
                        "$name: ",
                        style: TextStyle(
                          fontFamily: 'TiemposText',
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                ),
                TextSpan(
                  text: description,
                  style: TextStyle(
                    fontFamily: 'TiemposText',
                    fontSize: 14,
                    color: Colors.grey.shade800,
                  ),
                ),
              ],
            ),
          ),
        ),
        // Add divider after each attribute
        Divider(
          color: Colors.grey.shade200,
          height: 16, // Total height including the divider and spacing
          thickness: 1, // Thickness of the divider line
        ),
      ],
    );
  }

  // Helper method to build business rules section
  Widget _buildBusinessRulesSection(Entity entity) {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Policy Budget Rule
          _buildBusinessRule(
            'Policy Budget Rule',
            'Policies with Premium over \$1M require executive approval, Severity: Error',
          ),

          // Policy Timeline Rule
          _buildBusinessRule(
            'Policy Timeline Rule',
            'End date must be after start date, Severity: Error',
          ),
        ],
      ),
    );
  }

  // Helper method to build a single business rule
  Widget _buildBusinessRule(String name, String description) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TextStyle(
                    fontFamily: 'TiemposText',
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontFamily: 'TiemposText',
                    fontSize: 14,
                    color: Colors.grey.shade800,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build relationships section
  Widget _buildRelationshipsSection() {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // One-to-many with Order
          _buildRelationship(
            'One-to-many with',
            'Order',
            'via',
            'Customer ID',
          ),

          // One-to-one with Customer Preference
          _buildRelationship(
            'One-to-one with',
            'Customer Preference',
            'via',
            'Customer ID',
          ),

          // Many-to-many with Product
          _buildRelationship(
            'Many-to-many with',
            'Product',
            'via',
            'Wishlist Item',
          ),
        ],
      ),
    );
  }

  // Helper method to build a single relationship
  Widget _buildRelationship(
      String type, String entity, String via, String field) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
          Expanded(
            child: Wrap(
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                Text(
                  type,
                  style: TextStyle(
                    fontFamily: 'TiemposText',
                    fontSize: 14,
                  ),
                ),
                SizedBox(width: 4),
                Text(
                  entity,
                  style: TextStyle(
                      fontFamily: 'TiemposText',
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.black),
                ),
                SizedBox(width: 4),
                Text(
                  via,
                  style: TextStyle(
                    fontFamily: 'TiemposText',
                    fontSize: 14,
                  ),
                ),
                SizedBox(width: 4),
                field == "Customer ID"
                    ? _buildFieldWithTooltip(field)
                    : Text(
                        field,
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontFamily: 'TiemposText',
                            fontSize: 14,
                            color: Colors.black),
                      ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build circuit board section
  Widget _buildCircuitBoardSection() {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tabs
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildCircuitTab(
                    'Synthetic', selectedCircuitTab == 'Synthetic'),
                _buildCircuitTab(
                    'Classification', selectedCircuitTab == 'Classification'),
                _buildCircuitTab(
                    'Distribution', selectedCircuitTab == 'Distribution'),
                _buildCircuitTab('Loading Strategy',
                    selectedCircuitTab == 'Loading Strategy'),
              ],
            ),
          ),
          SizedBox(height: 16),

          // Name, Status And Email
          Tooltip(
            message:
                'Generates realistic test data for development and testing',
            preferBelow: false, // show above the text
            verticalOffset: 10, // reduce to e.g., 4 for minimal gap
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 8,
                  offset: Offset(2, 2),
                )
              ],
            ),
            textStyle: TextStyle(
              color: Colors.black,
              fontSize: 13,
            ),
            padding: EdgeInsets.all(8),
            child: Text(
              'Name, Status And Email',
              style: TextStyle(
                fontFamily: 'TiemposText',
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),

          SizedBox(height: 12),

          // Public
          _buildCircuitCategory('Public:', 'Name, Status, Email'),

          // Internal
          _buildCircuitCategory('Internal:', 'Phone Number, Address'),

          // Confidential
          _buildCircuitCategory('Confidential:', 'Date Of Birth, Age'),

          // Restricted
          _buildCircuitCategory('Restricted:', 'Customer Id'),
        ],
      ),
    );
  }

  // Helper method to build circuit board tab
  Widget _buildCircuitTab(String label, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedCircuitTab = label;
        });
      },
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          child: IntrinsicWidth(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: isSelected ? Colors.black : Colors.grey.shade700,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                    fontSize: 13,
                  ),
                ),
                SizedBox(height: 4),
                Container(
                  height: 2,
                  color: isSelected ? Colors.black : Colors.transparent,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build circuit category
  Widget _buildCircuitCategory(String category, String fields) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            category,
            style: TextStyle(
              fontFamily: 'TiemposText',
              fontSize: 14,
            ),
          ),
          SizedBox(width: 4),
          Expanded(
            child: Text(
              fields,
              style: TextStyle(
                fontFamily: 'TiemposText',
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build constants and validations section
  Widget _buildConstantsAndValidationsSection() {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Max Length For Name
          _buildValidation('Max Length For Name Is 100'),

          // Default Value For Status
          _buildValidation('Default Value For Status Is "Pending"'),
        ],
      ),
    );
  }

  // Helper method to build a single validation rule
  Widget _buildValidation(String rule) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('• ',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                fontFamily: 'TiemposText',
              )),
          Expanded(
            child: Text(
              rule,
              style: TextStyle(
                fontFamily: 'TiemposText',
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Custom resizable panel widget
  Widget _buildResizablePanel({
    required Widget child,
    required double width,
    required double minWidth,
    required double maxWidth,
    required Function(double) onResize,
  }) {
    return Stack(
      children: [
        // Main content
        SizedBox(
          width: width,
          child: child,
        ),

        // Resize handle
        Positioned(
          left: 0,
          top: 0,
          bottom: 0,
          child: GestureDetector(
            onHorizontalDragStart: (_) {
              setState(() {
                isResizing = true;
              });
            },
            onHorizontalDragUpdate: (details) {
              // Calculate new width by subtracting drag delta
              final newWidth = width - details.delta.dx;
              // Ensure width stays within bounds
              if (newWidth >= minWidth && newWidth <= maxWidth) {
                onResize(newWidth);
              }
            },
            onHorizontalDragEnd: (_) {
              setState(() {
                isResizing = false;
              });
            },
            child: MouseRegion(
              cursor: SystemMouseCursors.resizeLeftRight,
              onEnter: (_) {
                setState(() {
                  isResizing = true;
                });
              },
              onExit: (_) {
                setState(() {
                  isResizing = false;
                });
              },
              child: Container(
                width: 12,
                color: Colors.transparent,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 4,
                        height: 20,
                        margin: EdgeInsets.symmetric(vertical: 4),
                        decoration: BoxDecoration(
                          color: isResizing
                              ? Colors.blue.shade700
                              : Colors.grey.withValues(alpha: 128),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Helper method to check if text would be truncated with ellipsis
  bool _wouldTextBeTruncated(String text, TextStyle style, double maxWidth) {
    // First, measure the actual width of the text without constraints
    final TextPainter measuringPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      maxLines: 1,
      textDirection: TextDirection.ltr,
    )..layout(maxWidth: double.infinity);

    // Get the actual width of the text
    final double actualTextWidth = measuringPainter.width;

    // Removed verbose text measurement logging

    // Return true if the actual width exceeds the maximum width
    return actualTextWidth > maxWidth;
  }

  // Helper method to build entity card
  Widget _buildEntityCard(
      Entity entity, List<Entity> allEntities, EntityGroup group) {
    // If this is a relationship entity, hide it
    if (entity.relationType != null) {
      return SizedBox.shrink();
    }

    // If this is the first entity and we're showing related entities, highlight it
    // (No need to show a snackbar here as it's already shown in the click handler)

    final bool isSelected =
        selectedEntity != null && selectedEntity!.id == entity.id;

    // No tooltip highlighting needed

    // Create a unique global key for this entity card
    final GlobalKey entityCardKey =
        GlobalKey(debugLabel: 'entityCard_${entity.id}');

    // Check if the entire title row would be truncated
    bool wouldBeTruncated = false;

    double availableWidth = MediaQuery.of(context).size.width / 1.55;
    // availableWidth = availableWidth -
    //     (showSidePanel ? sidePanelWidth : 0) -
    //     (isChatHistoryExpanded ? MediaQuery.of(context).size.width / 6 : 0) -
    //     100;

    // Create a combined string of the entity title and attributes for truncation check
    String fullTitleText = entity.title ?? 'Untitled';
    if (entity.attributeString != null && entity.attributeString!.isNotEmpty) {
      fullTitleText += ' has ${entity.attributeString!}';
    }

    // Check if the combined text would be truncated
    wouldBeTruncated = _wouldTextBeTruncated(
        fullTitleText,
        TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
          color: Colors.black,
          fontFamily: 'TiemposText',
        ),
        availableWidth);

    // Removed verbose entity text logging

    // If there are related entities, we should use expansion tile regardless
    final bool shouldUseExpansionTile = wouldBeTruncated;

    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(0),
          decoration: BoxDecoration(
            color: isSelected ? Color(0xFFE3F2FD) : Colors.white,
            border: Border(
              top: BorderSide(color: Colors.grey.shade200),
              left: isSelected
                  ? BorderSide(color: Colors.blue.shade700, width: 4)
                  : BorderSide.none,
            ),
          ),
          child: Column(
            children: [
              shouldUseExpansionTile
                  ? _CustomExpansionTileWithEllipsis(
                      entity: entity,
                      entityCardKey: entityCardKey,
                      onExpansionChanged: (expanded) {
                        setState(() {
                          // Update the local entity for immediate UI response
                          entity.expanded = expanded;

                          // Update the entity's expanded state in the global data
                          globalEntitiesData.updateEntityExpandedState(
                              entity.id ?? '', expanded);
                        });
                      },
                      backgroundColor: Colors.transparent,
                      children: [],
                      onTitleTap: () {
                        // Update last click time to prevent tooltip from showing
                        _lastClickTime = DateTime.now();

                        // Hide any existing tooltip when clicking
                        _hideProfileTooltip();

                        setState(() {
                          if (selectedEntity != null &&
                              selectedEntity!.id == entity.id) {
                            // Keep selected
                          } else {
                            selectedRole = null;
                            selectedEntity = entity;
                            selectedWorkflow = null;
                          }
                          showSidePanel = true;
                          selectedSectionIndex = 0;
                        });
                      },
                    )
                  : GestureDetector(
                      onTap: () {
                        // Update last click time to prevent tooltip from showing
                        _lastClickTime = DateTime.now();

                        // Hide any existing tooltip when clicking
                        _hideProfileTooltip();

                        setState(() {
                          if (selectedEntity != null &&
                              selectedEntity!.id == entity.id) {
                            // Keep selected
                          } else {
                            selectedRole = null;
                            selectedEntity = entity;
                            selectedWorkflow = null;
                          }
                          showSidePanel = true;
                          selectedSectionIndex = 0;
                        });
                      },
                      child: Padding(
                        padding:
                            EdgeInsets.symmetric(horizontal: AppSpacing.xs),
                        child: entityTitleWidget(entityCardKey, entity),
                      ),
                    ),

              // No relationship entities shown
            ],
          ),
        ),
      ],
    );
  }

  Widget entityTitleWidget(entityCardKey, entity, {allowEllipsis = true}) {
    // Removed verbose entity title logging

    // Get the latest entity state from the global data
    Entity currentEntity = entity;
    if (globalEntitiesData.entityGroups != null) {
      for (var group in globalEntitiesData.entityGroups!) {
        if (group.entities != null) {
          for (var e in group.entities!) {
            if (e.id == entity.id) {
              currentEntity = e;
              break;
            }
          }
        }
      }
    }

    // Use the current entity's expanded state to determine allowEllipsis
    allowEllipsis = !(currentEntity.expanded ?? false);

    return Row(
      key: entityCardKey,
      children: [
        // Container(
        //   padding: EdgeInsets.only(
        //       left: 0,
        //       right: AppSpacing.xxs,
        //       top: AppSpacing.xs,
        //       bottom: AppSpacing.xs),
        //   decoration: BoxDecoration(
        //     border: Border(
        //         right: BorderSide(color: Colors.grey.shade300, width: 1)),
        //   ),
        //   child: CustomCheckbox(
        //     initialValue:
        //         globalEntitiesData.getEntityCheckedState(entity.id ?? ''),
        //     onChanged: (bool value) {
        //       setState(() {
        //         // Update the entity's checked state in the global data
        //         globalEntitiesData.updateEntityCheckedState(
        //             entity.id ?? '', value);
        //       });
        //     },
        //     materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        //   ),
        // ),
        // SizedBox(width: AppSpacing.xs),
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: AppSpacing.xs),
            child: Row(
              children: [
                // Create a unique global key for the entity title
                Builder(
                  builder: (context) {
                    final GlobalKey titleKey =
                        GlobalKey(debugLabel: 'entityTitle_${entity.id}');

                    return MouseRegion(
                      cursor: SystemMouseCursors.click,
                      onEnter: (_) {
                        // Show profile tooltip only when hovering on title
                        _showProfileTooltip(titleKey, entity);
                      },
                      onExit: (_) {
                        // Hide profile tooltip when not hovering
                        _hideProfileTooltip();
                      },
                      child: Text(
                        entity.title ?? 'Untitled',
                        key: titleKey,
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            fontFamily: "TiemposText"),
                      ),
                    );
                  },
                ),
                SizedBox(width: AppSpacing.xxs),
                if (entity.attributeString != null &&
                    entity.attributeString!.isNotEmpty)
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          'has ',
                          style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.grey.shade700,
                              fontFamily: "TiemposText"),
                        ),
                        Expanded(
                          child: _buildAttributeStringWithPrimaryKeys(entity,
                              allowEllipsis: allowEllipsis),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Build entities response
  Widget _buildEntitiesResponse() {
    // Show loading indicator while entities are being loaded
    if (isLoadingEntities) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Loading entities data...',
              style: TextStyle(
                fontFamily: 'TiemposText',
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    // Use the already loaded entity groups from the class variable
    // No need to create a default group as we're using the loaded groups directly

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Header with icon
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Container(
            //   margin: EdgeInsets.only(
            //       // right:
            //       // 30,
            //       // AppSpacing.sm,
            //       top: 2),
            //   child: Image(
            //     image: AssetImage('assets/images/checklist_small.png'),
            //     // height: 200,
            //   ),
            //   // Icon(
            //   //   Icons.description_outlined,
            //   //   size: 20,
            //   //   color: Colors.amber.shade800,
            //   // ),
            // ),
            // SizedBox(width:12),
            // Expanded(
            //   child: Consumer<AuthProvider>(
            //     builder: (context, authProvider, _) {
            //       // Get the username from the user profile
            //       final String firstName = authProvider.user?.username ?? '';

            //       // Get the localized greeting text
            //       final String greeting = AppLocalizations.of(context)
            //           .translate('build.entitiesGreeting');

            //       // If we have a username, prepend it to the greeting
            //       final String displayText =
            //           firstName.isNotEmpty ? '$firstName, $greeting' : greeting;

            //       return Text(
            //         displayText,
            //         style: TextStyle(
            //           fontFamily: 'TiemposText',
            //           fontSize: 14,
            //           fontWeight: FontWeight.w500,
            //         ),
            //       );
            //     },
            //   ),
            // ),
            SvgPicture.asset('assets/images/icons/box.svg',
                // fit: BoxFit.fill,
                height: 24,
                width: 24),
          ],
        ),
        SizedBox(height: AppSpacing.xs),

        // Entity groups
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: AppSpacing.xxs,
            vertical: AppSpacing.xxs,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppSpacing.xxs),
            border: Border.all(color: Colors.grey.shade300),
          ),
          // margin: EdgeInsets.only(left: 24), // Indent the content
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Container(
              padding: EdgeInsets.only(
                // horizontal: AppSpacing.xs,
                top: AppSpacing.xxs,
                bottom: AppSpacing.xs,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: AppSpacing.sm),
                    child: Text(globalEntitiesData.systemInfo?.headerText ?? '',
                        style: TextStyle(
                          fontFamily: 'TiemposText',
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        )),
                  ),
                  // Custom dropdown that looks exactly like the image
                  globalEntitiesData.systemInfo?.enitiyVersions != null &&
                          (globalEntitiesData.systemInfo!.enitiyVersions!)
                              .isNotEmpty
                      ? CustomEntityVersionDropdown(
                          versions: globalEntitiesData
                              .systemInfo!.enitiyVersions!
                              .map((v) => v.toString())
                              .toList(),
                          selectedVersion:
                              globalEntitiesData.getSelectedVersion(
                                  globalEntitiesData.systemInfo?.entityName ??
                                      ''),
                          onVersionSelected: (String version) {
                            setState(() {
                              globalEntitiesData.setSelectedVersion(
                                  globalEntitiesData.systemInfo?.entityName ??
                                      '',
                                  version);
                            });
                          },
                        )
                      : Container(),
                ],
              ),
            ),
            for (var group in globalEntitiesData.entityGroups ?? [])
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Group header
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSpacing.xs,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      // borderRadius: BorderRadius.only(
                      //   topLeft: Radius.circular(AppSpacing.xxs),
                      //   topRight: Radius.circular(AppSpacing.xxs),
                      // ),
                      border: Border(
                        top: BorderSide(color: Colors.grey.shade300),
                        left: BorderSide(color: Colors.grey.shade300),
                        right: BorderSide(color: Colors.grey.shade300),
                      ),
                    ),
                    child: Row(
                      children: [
                        // Container(
                        //   padding: EdgeInsets.only(
                        //       left: 0,
                        //       right: AppSpacing.xxs,
                        //       top: AppSpacing.xs,
                        //       bottom: AppSpacing.xs),
                        //   decoration: BoxDecoration(
                        //     border: Border(
                        //         right: BorderSide(
                        //             color: Colors.grey.shade300, width: 1)),
                        //   ),
                        //   child: CustomCheckbox(
                        //     initialValue: globalEntitiesData
                        //         .getGroupCheckedState(group.id ?? ''),
                        //     onChanged: (bool value) {
                        //       setState(() {
                        //         // Update the group's checked state in the global data
                        //         globalEntitiesData.updateGroupCheckedState(
                        //             group.id ?? '', value);
                        //       });
                        //     },
                        //     materialTapTargetSize:
                        //         MaterialTapTargetSize.shrinkWrap,
                        //   ),
                        // ),
                        // SizedBox(width: AppSpacing.xs),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                vertical: AppSpacing.xs),
                            child: Text(
                              'Entity: ${group.title}',
                              style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 12,
                                  fontFamily: 'TiemposText'),
                            ),
                          ),
                        ),
                        // // Custom dropdown that looks exactly like the image
                        // group.enitiyVersions != null && group.enitiyVersions!.isNotEmpty
                        //     ? CustomEntityVersionDropdown(
                        //         versions: group.enitiyVersions!.map((v) => v.toString()).toList(),
                        //         selectedVersion: globalEntitiesData.getSelectedVersion(group.id ?? ''),
                        //         onVersionSelected: (String version) {
                        //           setState(() {
                        //             globalEntitiesData.setSelectedVersion(group.id ?? '', version);
                        //           });
                        //         },
                        //       )
                        //     : Container(),
                        SizedBox(width: AppSpacing.sm),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: AppSpacing.sm,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: (group.coreCount != null &&
                                    group.coreCount!.contains('Core'))
                                ? Colors.amber.shade100
                                : Color(0xffCADEB9),
                            borderRadius: BorderRadius.circular(AppSpacing.xxs),
                          ),
                          child: Text(
                            group.coreCount ?? '',
                            style: TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.w500,
                              fontSize: 12,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Entity cards
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      // borderRadius: BorderRadius.only(
                      //   bottomLeft: Radius.circular(AppSpacing.sm),
                      //   bottomRight: Radius.circular(AppSpacing.sm),
                      // ),
                      color: Colors.white,
                    ),
                    child: Column(
                      children: (group.entities ?? []).map<Widget>((entity) {
                        // Create a non-nullable list for the _buildEntityCard method
                        final List<Entity> entityList = group.entities ?? [];
                        return _buildEntityCard(entity, entityList, group);
                      }).toList(),
                    ),
                  ),

                  (globalEntitiesData.entityGroups?.indexOf(group) ?? 0) <
                          (globalEntitiesData.entityGroups?.length ?? 1) - 1
                      ? SizedBox(height: AppSpacing.xl)
                      : SizedBox(height: 0),
                ],
              ),
          ]),
        ),

        // // System Info Section - Always shown at the end
        // if (globalEntitiesData.systemInfo != null)
        //   Container(
        //     margin: EdgeInsets.only(top: 10, bottom: 10),
        //     decoration: BoxDecoration(
        //       color: Colors.transparent,
        //       // borderRadius: BorderRadius.circular(AppSpacing.sm),
        //       // border: Border.all(color: Colors.grey.shade300),
        //     ),
        //     padding: EdgeInsets.only(top: AppSpacing.xs, bottom: AppSpacing.xs),
        //     child: Column(
        //       crossAxisAlignment: CrossAxisAlignment.start,
        //       children: [
        //         // System Info Header

        //         // Bullet points
        //         ...(globalEntitiesData.systemInfo?.bulletPoints ?? [])
        //             .map((point) => Padding(
        //                   padding: EdgeInsets.only(bottom: 8),
        //                   child: Row(
        //                     crossAxisAlignment: CrossAxisAlignment.start,
        //                     children: [
        //                       Text('• ',
        //                           style: TextStyle(
        //                             fontWeight: FontWeight.bold,
        //                             color: Colors.blue.shade800,
        //                           )),
        //                       Expanded(
        //                         child: Text(
        //                           point,
        //                           style: TextStyle(
        //                             fontFamily: 'TiemposText',
        //                             fontSize: 14,
        //                             fontWeight: FontWeight.w500,
        //                             color: Colors.black,
        //                           ),
        //                         ),
        //                       ),
        //                     ],
        //                   ),
        //                 )),
        //       ],
        //     ),
        //   ),
      ],
    );
  }

  // Helper method to get relation color based on relation type
  Color getRelationColor(String? relationType) {
    switch (relationType) {
      case 'nested':
        return Colors.green.shade700; // Green
      case 'shared':
        return Colors.blue.shade700; // Blue
      case 'junction':
        return Colors.orange.shade700; // Orange
      case 'agent':
        return Colors.purple.shade700; // Purple
      case 'workflow':
        return Colors.red.shade700; // Red
      default:
        return Colors.grey.shade700; // Default
    }
  }

  // Helper method to get relation icon based on relation type
  IconData getRelationIcon(String? relationType) {
    switch (relationType) {
      case 'junction':
        return Icons.link; // Link icon for junction
      case 'nested':
        return Icons.account_tree_outlined; // Tree icon for nested
      case 'shared':
        return Icons.share; // Share icon for shared
      case 'agent':
        return Icons.person; // Person icon for agent
      case 'workflow':
        return Icons.work; // Work icon for workflow
      default:
        return Icons.data_object_outlined; // Default
    }
  }

  // Helper method to build attribute string with primary key indicators
  Widget _buildAttributeStringWithPrimaryKeys(Entity entity,
      {bool allowEllipsis = true}) {
    // Get the latest entity state from the global data
    Entity currentEntity = entity;
    if (globalEntitiesData.entityGroups != null) {
      for (var group in globalEntitiesData.entityGroups!) {
        if (group.entities != null) {
          for (var e in group.entities!) {
            if (e.id == entity.id) {
              currentEntity = e;
              break;
            }
          }
        }
      }
    }

    // Use the current entity's expanded state to determine allowEllipsis
    allowEllipsis = !(currentEntity.expanded ?? false);

    // Removed verbose attribute logging

    if (entity.attributeString == null || entity.attributeString!.isEmpty) {
      return Text('');
    }

    // If there are no attributes or no primary keys, just return the original string
    if (entity.attributes == null || entity.attributes!.isEmpty) {
      return Text(
        entity.attributeString ?? '',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
          color: Colors.black,
          fontFamily: 'TiemposText',
        ),
        overflow: allowEllipsis ? TextOverflow.ellipsis : TextOverflow.visible,
        softWrap: !allowEllipsis,
        maxLines: allowEllipsis ? 1 : null,
      );
    }

    // Create a map of attribute names to their isPk status
    final Map<String, bool> primaryKeyMap = {};
    for (var attribute in entity.attributes!) {
      if (attribute.name != null) {
        primaryKeyMap[attribute.name!] = attribute.isPk ?? false;
      }
    }

    // Split the attribute string by commas and "and"
    final String attributeString = entity.attributeString!;
    List<String> parts = [];

    // First split by "and"
    final andParts = attributeString.split(' and ');

    // Process each part split by "and"
    for (int i = 0; i < andParts.length; i++) {
      // For all parts except the last one, split by commas
      if (i < andParts.length - 1) {
        final commaParts = andParts[i].split(', ');
        parts.addAll(commaParts);
      } else {
        // For the last part, just add it directly
        parts.add(andParts[i]);
      }
    }

    // Create a list of text spans for each attribute
    List<InlineSpan> textSpans = [];

    for (int i = 0; i < parts.length; i++) {
      String part = parts[i].trim();

      // Check if this attribute is a primary key
      bool isPrimaryKey = false;
      for (var attrName in primaryKeyMap.keys) {
        if (part.contains(attrName) && primaryKeyMap[attrName] == true) {
          isPrimaryKey = true;
          break;
        }
      }

      // Add the attribute with or without PK indicator
      if (isPrimaryKey) {
        textSpans.add(
          TextSpan(
            text: part,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          ),
        );
        // Add a small space before the superscript
        textSpans.add(TextSpan(text: ' '));

        // Use WidgetSpan for better control over positioning
        textSpans.add(
          WidgetSpan(
            alignment: PlaceholderAlignment.top,
            child: Transform.translate(
              offset: Offset(0, -5), // Move it up to look like a superscript
              child: Text(
                '^PK',
                style: TextStyle(
                  fontSize: 9,
                  fontWeight: FontWeight.bold,
                  color: Color(0xff0058FF),
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
          ),
        );
      } else {
        textSpans.add(
          TextSpan(
            text: part,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          ),
        );
      }

      // Add separator (comma or "and") if not the last item
      if (i < parts.length - 1) {
        if (i == parts.length - 2) {
          textSpans.add(TextSpan(text: ' and '));
        } else {
          textSpans.add(TextSpan(text: ', '));
        }
      }
    }

    // Get the latest entity state from the global data
    Entity latestEntity = entity;
    if (globalEntitiesData.entityGroups != null) {
      for (var group in globalEntitiesData.entityGroups!) {
        if (group.entities != null) {
          for (var e in group.entities!) {
            if (e.id == entity.id) {
              latestEntity = e;
              break;
            }
          }
        }
      }
    }

    // Use the current entity's expanded state to determine allowEllipsis
    bool shouldUseEllipsis = !(latestEntity.expanded ?? false);

    return RichText(
      text: TextSpan(children: textSpans),
      overflow:
          shouldUseEllipsis ? TextOverflow.ellipsis : TextOverflow.visible,
      softWrap: !shouldUseEllipsis,
      maxLines: shouldUseEllipsis ? 1 : null,
    );
  }

  // Build workflows response
  Widget _buildWorkflowsResponse() {
    Logger.info('Building workflow response from PROVIDER data');
    // Get the workflow data provider
    final workflowDataProvider =
        Provider.of<WorkflowDataProvider>(context, listen: true);

    // Show loading indicator while workflows are being loaded
    if (workflowDataProvider.isLoadingWorkflows) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Loading workflows data...',
              style: TextStyle(
                fontFamily: 'TiemposText',
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    // Get the first workflow from the list
    final workflow = workflowDataProvider.workflows.isNotEmpty
        ? workflowDataProvider.workflows[0]
        : null;
    if (workflow == null) {
      return Center(
        child: Text(
          'No workflow data available.',
          style: TextStyle(
            fontFamily: 'TiemposText',
            fontSize: 16,
          ),
        ),
      );
    }

    // Get the tree data from the API response
    final List<dynamic> treeData = workflow['tree'] ?? [];
    final String mainTitle = workflow['mainTitle'] ?? '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header row with workflow info
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Consumer<AuthProvider>(
                builder: (context, authProvider, _) {
                  // Get the username from the user profile
                  final String firstName = authProvider.user?.username ?? '';

                  // Get the localized greeting text
                  final String greeting = AppLocalizations.of(context)
                      .translate('build.workflowGreeting');

                  // If we have a username, prepend it to the greeting
                  final String displayText =
                      firstName.isNotEmpty ? '$firstName, $greeting' : greeting;

                  return Text(
                    displayText,
                    style: TextStyle(
                      fontFamily: 'TiemposText',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  );
                },
              ),
            ),
            SvgPicture.asset('assets/images/eye.svg', height: 14, width: 18),
            SizedBox(width: 5),
            Text(
              'PREVIEW UI',
              style: TextStyle(
                fontSize: 9,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(width: 10),
            SvgPicture.asset('assets/images/icons/box.svg',
                height: 24, width: 24),
          ],
        ),

        SizedBox(height: AppSpacing.md),

        // Workflow visualization container
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(AppSpacing.xs),
            color: Colors.white,
          ),
          padding: EdgeInsets.all(AppSpacing.xxs),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Workflow header
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(AppSpacing.xs),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        "Workflow",
                        style: TextStyle(
                          fontFamily: 'TiemposText',
                          fontSize: 12.0,
                          color: Colors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Icon(Icons.folder_outlined,
                        size: 18, color: Colors.grey.shade600),
                    Icon(Icons.arrow_drop_down,
                        size: 18, color: Colors.grey.shade600),
                  ],
                ),
              ),

              SizedBox(height: AppSpacing.xs),

              // Workflow title
              Padding(
                padding: EdgeInsets.only(left: AppSpacing.xs),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Builder(builder: (context) {
                      final GlobalKey titleKey = GlobalKey(
                          debugLabel: 'workflowTitle_${workflow['id']}');

                      return InkWell(
                        onTap: () {
                          // Update last click time to prevent tooltip from showing
                          _lastClickTime = DateTime.now();

                          // Hide any existing tooltip when clicking
                          _hideWorkflowProfileTooltip();

                          setState(() {
                            selectedRole = null;
                            selectedEntity = null;
                            selectedWorkflow = workflow;
                            showSidePanel = true;

                            // Scroll the workflow tree horizontally to show content that might be overflowing
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              if (_workflowTreeScrollController.hasClients) {
                                _workflowTreeScrollController.animateTo(
                                  0, // Scroll to position 0 (leftmost content)
                                  duration: Duration(milliseconds: 500),
                                  curve: Curves.easeInOut,
                                );
                              }
                            });
                          });
                        },
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          onEnter: (_) {
                            // Only show tooltip if not already showing side panel for this workflow
                            if (!showSidePanel ||
                                selectedWorkflow != workflow) {
                              _showWorkflowProfileTooltip(titleKey, workflow);
                            }
                          },
                          onExit: (_) {
                            // Hide profile tooltip when not hovering
                            _hideWorkflowProfileTooltip();
                          },
                          child: Text(
                            mainTitle,
                            key: titleKey,
                            style: TextStyle(
                              fontFamily: 'TiemposText',
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              ),

              SizedBox(height: AppSpacing.md),

              // Workflow tree visualization using WorkflowTreeBuilder
              Padding(
                padding: EdgeInsets.all(AppSpacing.xs),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  controller: _workflowTreeScrollController,
                  child: WorkflowTreeBuilder.buildWorkflowTree(treeData),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppSpacing.md),
      ],
    );
  }

  // Vertical dotted connector line for Alt 1 branch

  // Build a horizontal workflow step (for the first row)
  Widget _buildHorizontalWorkflowStep(String text) {
    return Text(
      text,
      style: TextStyle(
        fontSize: 14,
        fontFamily: 'TiemposText',
        fontWeight: FontWeight.w500,
      ),
    );
  }

  // Build a row for parallel branch steps - new design matching the reference image
  Widget _buildParallelBranchRowNew(Map<String, dynamic> parallelChild) {
    // Get the child nodes for this branch
    final List<Map<String, dynamic>> branchNodes = [];

    // Add the level 1 node (Inventory Manager or Finance Officer)
    branchNodes.add(parallelChild);

    // Add level 2 node if it exists (System Completes Purchase Order)
    if (parallelChild.containsKey('children') &&
        parallelChild['children'] != null &&
        (parallelChild['children'] as List).isNotEmpty) {
      final level2Node =
          (parallelChild['children'] as List)[0] as Map<String, dynamic>;
      branchNodes.add(level2Node);

      // Add level 3 node if it exists (Vendor Receives Purchase Order or Delivery Receives Pickup Order)
      if (level2Node.containsKey('children') &&
          level2Node['children'] != null &&
          (level2Node['children'] as List).isNotEmpty) {
        final level3Node =
            (level2Node['children'] as List)[0] as Map<String, dynamic>;
        branchNodes.add(level3Node);
      }
    }

    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      decoration: BoxDecoration(
        color: Colors.amber.shade50.withValues(alpha: 51), // 0.2 * 255 = ~51
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Part label
          Container(
            margin: EdgeInsets.only(right: 8),
            child: Text(
              'Part ${parallelChild['sequence'] ?? 1}.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.amber.shade800,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // First node (Inventory Manager or Finance Officer)
          Text(
            branchNodes[0]['text'] ?? '',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),

          // Spacer
          SizedBox(width: 16),

          // Second node (System Completes Purchase Order)
          if (branchNodes.length >= 2)
            Text(
              branchNodes[1]['text'] ?? '',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),

          // Spacer
          if (branchNodes.length >= 2) SizedBox(width: 16),

          // Third node (Vendor Receives Purchase Order or Delivery Receives Pickup Order)
          if (branchNodes.length >= 3)
            Text(
              branchNodes[2]['text'] ?? '',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
        ],
      ),
    );
  }

  // Build a workflow tree node and its children
  Widget _buildWorkflowTreeNode(
      Map<String, dynamic> node, List<dynamic> allNodes, int indentLevel) {
    final bool hasChildren = node.containsKey('children') &&
        node['children'] != null &&
        (node['children'] as List).isNotEmpty;
    final String nodeText = node['text'] ?? '';
    final bool isRejection = node['isRejection'] ?? false;
    final bool isParallel = node['isParallel'] ?? false;
    final bool hasCheckmark = node['hasCheckmark'] ?? false;
    final bool hasX = node['hasX'] ?? false;
    final String altText = node['altText'] ?? '';

    // Check if this is an Alt path node
    final bool isAltPath = altText.isNotEmpty;

    return Stack(
      children: [
        // Vertical connector line for children
        if (hasChildren)
          Positioned(
            left: indentLevel * 24.0 + 8,
            top: 24,
            bottom: 0,
            width: 1,
            child: Container(
              color: Colors.grey.shade300,
            ),
          ),

        // Vertical connector line for siblings
        if (indentLevel > 0)
          Positioned(
            left: (indentLevel - 1) * 24.0 + 8,
            top: 0,
            bottom: 0,
            width: 1,
            child: Container(
              color: Colors.grey.shade300,
            ),
          ),

        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current node
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Indentation space with Alt text if applicable
                if (isAltPath)
                  SizedBox(
                    width: indentLevel * 24.0,
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: EdgeInsets.only(left: 4),
                        child: Text(
                          'Alt. $altText',
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ),
                    ),
                  )
                else
                  SizedBox(width: indentLevel * 24.0),

                // Circle indicator
                Container(
                  margin: EdgeInsets.only(top: 2, right: 8),
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.grey.shade400, width: 0.5),
                    color: Colors.white,
                  ),
                ),

                // Node content
                if (nodeText.isNotEmpty)
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: Text(
                            nodeText,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: isRejection
                                  ? Colors.red.shade700
                                  : isParallel
                                      ? Colors.amber.shade800
                                      : Colors.black,
                            ),
                          ),
                        ),

                        // Link icon for Procurement Officer step
                        if (nodeText.contains(
                            'Procurement Officer Creates Purchase Order'))
                          Container(
                            margin: EdgeInsets.only(left: 8),
                            child: Icon(
                              Icons.link,
                              color: Colors.green.shade700,
                              size: 20,
                            ),
                          ),

                        // Checkmark icon
                        if (hasCheckmark)
                          Container(
                            width: 20,
                            height: 20,
                            margin: EdgeInsets.only(left: 8),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.green.shade50,
                              border: Border.all(color: Colors.green.shade400),
                            ),
                            child: Icon(
                              Icons.check,
                              size: 14,
                              color: Colors.green.shade700,
                            ),
                          ),

                        // X icon for rejection
                        if (hasX ||
                            isRejection ||
                            nodeText.contains('Rejection'))
                          Container(
                            width: 20,
                            height: 20,
                            margin: EdgeInsets.only(left: 8),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.red.shade50,
                              border: Border.all(color: Colors.red.shade400),
                            ),
                            child: Icon(
                              Icons.close,
                              size: 14,
                              color: Colors.red.shade700,
                            ),
                          ),

                        // Parallel label
                        if (isParallel)
                          Container(
                            margin: EdgeInsets.only(left: 8),
                            padding: EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.amber.shade50,
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(color: Colors.amber.shade200),
                            ),
                            child: Text(
                              'Part.',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.amber.shade800,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
              ],
            ),

            // Children nodes
            if (hasChildren)
              Padding(
                padding: EdgeInsets.only(top: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (var child in (node['children'] as List<dynamic>))
                      _buildWorkflowTreeNode(child as Map<String, dynamic>,
                          allNodes, indentLevel + 1),
                  ],
                ),
              ),

            // Add spacing between top-level nodes
            if (indentLevel == 0) SizedBox(height: 24),
          ],
        ),

        // Horizontal connector line
        if (indentLevel > 0)
          Positioned(
            left: (indentLevel - 1) * 24.0 + 16,
            top: 10,
            child: Container(
              width: 16,
              height: 1,
              color: Colors.grey.shade300,
            ),
          ),
      ],
    );
  }

  // Helper method to get section abbreviation
  String _getSectionAbbreviation(String sectionName) {
    switch (sectionName) {
      case 'Attributes':
        return 'ATT';
      case 'Business Rules':
        return 'BR';
      case 'Relationships':
        return 'REL';
      case 'Circuit Board':
        return 'CB';
      case 'Constants & Validations':
        return 'CV';
      default:
        return sectionName.substring(0, 1);
    }
  }

  // Helper method to build field with tooltip
  Widget _buildFieldWithTooltip(String fieldName) {
    return _buildNewFieldTooltip(fieldName);
  }

  // New tooltip design matching the reference image
  Widget _buildNewFieldTooltip(String fieldName) {
    // Field-specific content
    List<Map<String, String>> tooltipItems = [];

    // Configure content based on field name
    switch (fieldName) {
      case 'Customer ID':
        tooltipItems = [
          {
            'text':
                'On Delete: Cascade (Delete Orders when Customer is deleted)',
            'prefix': '• '
          },
          {
            'text':
                'On Update: Cascade (Update details in Order when detail in Customer is updated)',
            'prefix': '• '
          },
        ];
        break;
      case 'Name':
        tooltipItems = [
          {
            'text': 'On Delete: Restrict (Cannot delete if referenced)',
            'prefix': '• '
          },
          {
            'text': 'On Update: Cascade (Update related records)',
            'prefix': '• '
          },
        ];
        break;
      case 'Email':
        tooltipItems = [
          {'text': 'On Delete: No Action', 'prefix': '• '},
          {'text': 'On Update: No Action', 'prefix': '• '},
        ];
        break;
      case 'Phone Number':
        tooltipItems = [
          {'text': 'On Delete: Set Null', 'prefix': '• '},
          {'text': 'On Update: Cascade', 'prefix': '• '},
        ];
        break;
      case 'Address':
        tooltipItems = [
          {'text': 'On Delete: No Action', 'prefix': '• '},
          {'text': 'On Update: No Action', 'prefix': '• '},
        ];
        break;
      case 'Gender':
        tooltipItems = [
          {'text': 'On Delete: No Action', 'prefix': '• '},
          {'text': 'On Update: No Action', 'prefix': '• '},
        ];
        break;
      default:
        tooltipItems = [
          {'text': 'On Delete: No Action', 'prefix': '• '},
          {'text': 'On Update: No Action', 'prefix': '• '},
        ];
    }

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: Tooltip(
        richMessage: WidgetSpan(
          child: Container(
            width: 350,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 51), // 0.2 * 255 = ~51
                  blurRadius: 10,
                  offset: Offset(0, 3),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Blue header with icon
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade700,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      // Left side - Icon with white background
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.list_alt,
                          color: Colors.blue.shade700,
                          size: 24,
                        ),
                      ),
                      SizedBox(width: 12),
                      // Right side - ID and version info
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                '• ID: at001',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(width: 8),
                              Text(
                                '• Version: V00012',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 4),
                          Row(
                            children: [
                              Text(
                                '• Created: 12/4/2025 by john smith',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 4),
                          Row(
                            children: [
                              Text(
                                '• Last Modified: 15/5/2025 by jan dae',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Editable Properties section
                Container(
                  padding: EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Editable Properties:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      SizedBox(height: 8),
                      ...tooltipItems.map((item) => Padding(
                            padding: EdgeInsets.only(bottom: 4),
                            child: Text(
                              '${item['prefix'] ?? ''}${item['text'] ?? ''}',
                              style: TextStyle(
                                fontSize: 13,
                                color: Colors.black87,
                              ),
                            ),
                          )),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        preferBelow: false,
        verticalOffset: 20,
        padding: EdgeInsets.zero,
        margin: EdgeInsets.zero,
        showDuration: Duration(seconds: 10),
        decoration: BoxDecoration(
          color: Colors.transparent,
          boxShadow: [],
        ),
        child: Text(
          fieldName,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontFamily: 'TiemposText',
            fontSize: 14,
            color: Colors.black,
          ),
        ),
      ),
    );
  }

  // Convert text to speech and play audio
  Future<void> _convertTextToSpeech(String text, {String? messageId}) async {
    if (text.isEmpty) return;

    // Show loading indicator
    setState(() {
      isLoading = true;
    });

    try {
      // Call the multimedia service to convert text to speech
      await _multimediaService.convertTextToSpeech(
        text,
        messageId: messageId,
        context: context,
      );
    } finally {
      // Hide loading indicator
      setState(() {
        isLoading = false;
      });
    }
  }

  // Show a small overlay notification for copy action
  void _showCopyOverlay(BuildContext context) {
    // Create an overlay entry
    final OverlayEntry overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: 50,
        right: 50,
        child: Material(
          elevation: 4.0,
          borderRadius: BorderRadius.circular(8),
          color: Colors.black87,
          child: Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.check_circle_outline,
                  color: Colors.white,
                  size: 16,
                ),
                SizedBox(width: 8),
                Text(
                  'Copied to clipboard',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    // Add the overlay to the overlay
    Overlay.of(context).insert(overlayEntry);

    // Remove the overlay after 2 seconds
    Future.delayed(Duration(seconds: 2), () {
      overlayEntry.remove();
    });
  }

  // Toggle recording (start/stop)
  Future<void> _toggleRecording() async {
    await _multimediaService.toggleRecording(context);
  }

  // Build a full-width recording UI for web audio recorder
  Widget _buildFullWidthRecordingUI(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: _multimediaService.createWebAudioRecorderWidget(
        chatController: chatController,
        onCancel: () {
          setState(() {
            isRecording = false;
          });
        },
        onLoadingChanged: (loading) {
          setState(() {
            isLoading = loading;
          });
        },
      ),
    );
  }

  // Handle sending a message
  void _sendMessage() {
    final text = chatController.text.trim();
    if (text.isEmpty) return;

    final provider = Provider.of<WebHomeProvider>(context, listen: false);

    // Prepare message content
    String messageContent = text.replaceFirst(text[0], text[0].toUpperCase());

    // If a file is uploaded, include the file information in the message
    Map<String, dynamic>? fileData;
    String finalMessageContent = messageContent;

    if (isFileUploaded) {
      fileData = {
        'fileName': uploadedFileName,
        'extractedText': uploadedFileText,
      };

      // Concatenate the extracted text with the user input for the API call
      // But keep the original message content for display
      if (uploadedFileText.isNotEmpty) {
        Logger.info(
            'Concatenating extracted text with user input for API call');
        Logger.info('Original message: $messageContent');
        Logger.info('Extracted text: $uploadedFileText');

        // Only concatenate if the user hasn't already included the OCR text
        if (!messageContent.contains(uploadedFileText)) {
          finalMessageContent = "$uploadedFileText\n\n$messageContent";
          Logger.info('Final message for API: $finalMessageContent');
        }
      }

      // Reset file upload state after sending
      setState(() {
        isFileUploaded = false;
      });
    }

    setState(() {
      // Add user message to the UI (display only the original message)
      provider.addMessage(ChatMessage(
        content: messageContent,
        isUser: true,
        fileData: fileData,
      ));

      // Clear input field
      chatController.text = '';

      // Show loading indicator
      isLoading = true;
    });

    // Store the final message content (with OCR text if available) for API calls
    provider.lastUserMessageForApi = finalMessageContent;

    // Check which quick message is selected
    if (selectedQuickMessage == 'General') {
      // Call the general API
      provider.sendGeneralQuestion(text).then((response) async {
        await _processApiResponseWithTranslation(response, 'General');

        // Scroll to bottom after the UI updates
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      }).catchError((error) {
        setState(() {
          isLoading = false;
          provider.addMessage(ChatMessage(
            content: 'Error: $error',
            isUser: false,
          ));
        });

        // Scroll to bottom after the UI updates
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      });
    } else if (selectedQuickMessage == 'Internet') {
      // Call the internet API with user ID from AuthService
      provider.sendInternetQuestion(text).then((response) async {
        await _processApiResponseWithTranslation(response, 'Internet');

        // Scroll to bottom after the UI updates
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      }).catchError((error) {
        setState(() {
          isLoading = false;
          provider.addMessage(ChatMessage(
            content: 'Error: $error',
            isUser: false,
          ));
        });

        // Scroll to bottom after the UI updates
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      });
    } else if (selectedQuickMessage == 'NSL') {
      // Call the NSL API with user ID from AuthService
      provider.sendNslQuestion(text).then((response) async {
        await _processApiResponseWithTranslation(response, 'NSL');

        // Scroll to bottom after the UI updates
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      }).catchError((error) {
        setState(() {
          isLoading = false;
          provider.addMessage(ChatMessage(
            content: 'Error: $error',
            isUser: false,
          ));
        });

        // Scroll to bottom after the UI updates
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      });
    } else if (selectedQuickMessage == "Solution") {
      // Original behavior for other quick messages
      // final lowerText = text.toLowerCase();
      // bool showLocalData = false;

      // for (String element in localData) {
      //   if (lowerText.contains(element)) {
      //     showLocalData = true;
      //     break;
      //   }
      // }

      // if (showLocalData) {
      //   Future.delayed(Duration(seconds: 1), () {
      //     setState(() {
      //       isLoading = false;

      //       // Add NSL response with custom content based on the message
      //       if (lowerText.contains('role') || lowerText.contains('agent')) {
      //         messages.add(ChatMessage(
      //           content: '',
      //           isUser: false,
      //           customContent: _buildRolesResponse(),
      //         ));
      //       } else if (lowerText.contains('entity') ||
      //           lowerText.contains('entities')) {
      //         messages.add(ChatMessage(
      //           content: '',
      //           isUser: false,
      //           customContent: _buildEntitiesResponse(),
      //         ));
      //       } else if (lowerText.contains('workflow')) {
      //         messages.add(ChatMessage(
      //           content: '',
      //           isUser: false,
      //           customContent: _buildWorkflowsResponse(),
      //         ));
      //       } else {
      //         // Generic response for other messages
      //         messages.add(ChatMessage(
      //           content:
      //               'I can help you with that. Would you like me to create a solution for you?',
      //           isUser: false,
      //         ));
      //       }
      //     });
      //   });
      // } else {
      //   _handleConversation(text);
      // }

      _handleConversation(text);

      // Scroll to bottom after the UI updates
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    } else {
      _handleConversation(text);

      // Scroll to bottom after the UI updates
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    }
  }

  Widget chatField(BuildContext context, {double? width, double? height}) {
    // If recording is active, show a full-width recording UI
    if (isRecording && _multimediaService.shouldUseWebAudioRecorder()) {
      return Container(
        margin: EdgeInsets.symmetric(
          vertical: AppSpacing.xl,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppSpacing.md),
          border: Border.all(color: Color(0xffD0D0D0), width: 0.5),
        ),
        constraints: BoxConstraints(
          maxHeight: 400,
          minHeight: height ?? 110,
        ),
        child: _buildFullWidthRecordingUI(context),
      );
    }

    // Otherwise, show the normal chat field
    return Container(
      margin: EdgeInsets.symmetric(
        vertical: AppSpacing.xl,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSpacing.md),
        border: Border.all(color: Color(0xffD0D0D0), width: 0.5),
      ),
      constraints: BoxConstraints(
        maxHeight: 400,
        minHeight: height ?? 110,
      ),
      padding: EdgeInsets.only(
          top: AppSpacing.xxs, left: AppSpacing.xxs, right: AppSpacing.xxs),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Show file upload preview if a file is uploaded
          if (isFileUploaded)
            FileUploadPreview(
              fileName: uploadedFileName,
              onClose: () {
                setState(() {
                  isFileUploaded = false;
                  uploadedFileName = '';
                  uploadedFileText = '';
                  // Clear the text field if it contains the OCR text
                  if (chatController.text == uploadedFileText) {
                    chatController.clear();
                  }
                });
              },
            ),

          Flexible(
            child: TextField(
              cursorHeight: 14,
              controller: chatController,
              maxLines: 1,
              enabled: !isLoading,
              decoration: InputDecoration(
                focusColor: Colors.transparent,
                hintStyle: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.normal,
                  color: Colors.grey,
                  fontFamily: "TiemposText",
                ),
                hintText: isLoading
                    ? context.tr('home.sendingMessage')
                    : context.tr('home.askNSL'),
                hoverColor: Colors.transparent,
                border: OutlineInputBorder(borderSide: BorderSide.none),
                enabledBorder: OutlineInputBorder(borderSide: BorderSide.none),
                focusedBorder: OutlineInputBorder(borderSide: BorderSide.none),
                errorBorder: OutlineInputBorder(borderSide: BorderSide.none),
                disabledBorder: OutlineInputBorder(borderSide: BorderSide.none),
              ),
              onSubmitted: isLoading ? null : (_) => _sendMessage(),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Add button is always shown
              _AddButtonWithMenu(this),

              // Show either mic button or arrow button based on text state
              if (hasTextInChatField)
                // Show arrow button when there's text
                hoverButtons(
                  icon: isLoading
                      ? Icon(Icons.stop) // Show stop icon when loading
                      : Icon(Icons.arrow_upward),
                  onPressed: isLoading ? _cancelRequest : _sendMessage,
                )
              else if (isRecording &&
                  !_multimediaService.shouldUseWebAudioRecorder())
                // Show speech recognition UI when recording (non-web platforms only)
                Row(
                  children: [
                    SpeechRecognitionWidget(
                      recognizedText: _recognizedText,
                      onCancel: () {
                        _multimediaService.stopSpeechRecognition();
                      },
                      onConfirm: () {
                        // Get the latest recognized text directly from the service
                        final latestText =
                            _multimediaService.getRecognizedText();

                        if (latestText.isNotEmpty) {
                          setState(() {
                            // Update the chat field with the latest recognized text
                            chatController.text = latestText;

                            // Also update our local state
                            _recognizedText = latestText;
                          });

                          Logger.info(
                              "Setting chat field text to: $latestText");
                        } else {
                          Logger.info("No recognized text available");
                        }

                        _multimediaService.stopSpeechRecognition();
                      },
                    ),
                  ],
                )
              else
                // Show mic button when there's no text and not recording
                hoverButtons(
                    icon: isLoading ? Icon(Icons.stop) : Icon(Icons.mic_none),
                    onPressed: isLoading ? _cancelRequest : _toggleRecording),
            ],
          )
        ],
      ),
    );
  }

  // Get image path for quick message type
  String getQuickMessageImagePath(String text) {
    switch (text) {
      case 'NSL':
        return 'assets/images/book_nsl.svg';
      case 'Solution':
        return 'assets/images/bulb_solution.svg';
      case 'General':
        return 'assets/images/loader_general.svg';
      case 'Internet':
        return 'assets/images/internet.svg';
      default:
        return 'assets/images/quickmessage/chat_icon.png';
    }
  }

  Widget quickMessage({required String text}) {
    // Get the provider
    final provider = Provider.of<WebHomeProvider>(context, listen: false);

    // Create a custom stateful widget for hover effect with selection state
    return _QuickMessageButton(
      text: text,
      imagePath: getQuickMessageImagePath(text),
      isSelected: provider.selectedQuickMessage == text,
      onTap: () {
        // Toggle selection if already selected, otherwise select this message
        provider.selectedQuickMessage =
            provider.selectedQuickMessage == text ? null : text;

        if (provider.selectedQuickMessage != null) {
          chatController.text = ''; // Clear any existing text
        }
      },
    );
  }
}

class _HoverButton extends StatefulWidget {
  final Widget icon;
  final VoidCallback? onPressed;

  const _HoverButton({
    required this.icon,
    this.onPressed,
  });

  @override
  State<_HoverButton> createState() => _HoverButtonState();
}

class _HoverButtonState extends State<_HoverButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    final bool isDisabled = widget.onPressed == null;

    return MouseRegion(
      onEnter: isDisabled ? null : (_) => setState(() => isHovered = true),
      onExit: isDisabled ? null : (_) => setState(() => isHovered = false),
      cursor:
          isDisabled ? SystemMouseCursors.forbidden : SystemMouseCursors.click,
      child: Container(
        height: 28,
        width: 28,
        margin: EdgeInsets.symmetric(
            horizontal: AppSpacing.xs, vertical: AppSpacing.sm),
        padding: EdgeInsets.zero,
        decoration: BoxDecoration(
            color: isDisabled
                ? Colors.grey.shade300
                : isHovered
                    ? Theme.of(context).colorScheme.primary
                    : Color(0xffE4EDFF),
            borderRadius: BorderRadius.circular(AppSpacing.lg)),
        child: IconButton(
          padding: EdgeInsets.zero,
          onPressed: widget.onPressed,
          icon: widget.icon,
          iconSize: 18,
          color: isDisabled
              ? Colors.grey.shade500
              : isHovered
                  ? Colors.white
                  : null,
        ),
      ),
    );
  }
}

// A stateful timer widget that starts immediately
class _RecordingTimer extends StatefulWidget {
  @override
  State<_RecordingTimer> createState() => _RecordingTimerState();
}

class _RecordingTimerState extends State<_RecordingTimer> {
  int _seconds = 0;
  late Timer _timer;

  @override
  void initState() {
    super.initState();
    // Start the timer immediately
    _startTimer();
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  void _startTimer() {
    // Start immediately with 0 seconds
    setState(() {
      _seconds = 0;
    });

    // Update every second
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
        _seconds++;
      });
    });
  }

  String _formatDuration() {
    final minutes = (_seconds ~/ 60).toString().padLeft(2, '0');
    final seconds = (_seconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      _formatDuration(),
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }
}

class _QuickMessageButton extends StatefulWidget {
  final String text;
  final String imagePath;
  final VoidCallback onTap;
  final bool isSelected;

  const _QuickMessageButton({
    required this.text,
    required this.imagePath,
    required this.onTap,
    required this.isSelected,
  });

  @override
  State<_QuickMessageButton> createState() => _QuickMessageButtonState();
}

class _QuickMessageButtonState extends State<_QuickMessageButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: InkWell(
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        onTap: widget.onTap,
        child: Container(
          // No fixed width constraints - allow width to adapt to content
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(
              color: widget.isSelected
                  ? Color(0xff0058FF) // Blue color for selected state
                  : isHovered
                      ? Color(0xff0058FF)
                      : Color(0xffD0D0D0),
              width: 1.0,
            ),
            borderRadius: BorderRadius.circular(4),
            // boxShadow: (isHovered)
            //     ? [
            //         BoxShadow(
            //           color: Color(0xff9FB8F7)
            //               .withValues(alpha: 77), // 0.3 * 255 ≈ 77
            //           blurRadius: 3,
            //           spreadRadius: 1,
            //         )
            //       ]
            //     : [],
          ),
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Image
              SvgPicture.asset(widget.imagePath,
                  width: 16,
                  height: 16,
                  colorFilter: widget.isSelected || isHovered
                      ? ColorFilter.mode(
                          Color(0xff0058FF),
                          BlendMode
                              .srcIn) // Blue color for selected or hover state
                      : ColorFilter.mode(Colors.black,
                          BlendMode.srcIn) // Always use black color by default
                  ),
              SizedBox(width: 4),
              // Text
              Text(
                widget.text,
                style: TextStyle(
                  fontSize: 14,
                  fontFamily: 'TiemposText',
                  color: Colors.black,
                  fontWeight: FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Chat history item widget with hover effects
class _ChatHistoryItemWidget extends StatefulWidget {
  final Map<String, dynamic> chat;
  final Color backgroundColor;
  // final IconData chatTypeIcon;
  final VoidCallback onTap;
  final Function formatTimestamp;
  final bool isExpanded;

  const _ChatHistoryItemWidget({
    required this.chat,
    required this.backgroundColor,
    // required this.chatTypeIcon,
    required this.onTap,
    required this.formatTimestamp,
    required this.isExpanded,
  });

  @override
  State<_ChatHistoryItemWidget> createState() => _ChatHistoryItemWidgetState();
}

class _ChatHistoryItemWidgetState extends State<_ChatHistoryItemWidget> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    // If not expanded, don't render anything
    if (!widget.isExpanded) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        MouseRegion(
          onEnter: (_) => setState(() => isHovered = true),
          onExit: (_) => setState(() => isHovered = false),
          child: InkWell(
            onTap: widget.onTap,
            hoverColor: Colors.transparent,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            splashFactory: NoSplash.splashFactory,
            child: Container(
              // margin: EdgeInsets.symmetric(horizontal: 2),
              padding: EdgeInsets.symmetric(horizontal: 2, vertical: 7),
              decoration: BoxDecoration(
                color: isHovered ? Color(0xffF2F2F2) : Colors.transparent,
                // border: Border(
                //   bottom: BorderSide(
                //     color: Colors.grey.shade300,
                //     width: 1,
                //   ),
                // ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Chat type icon
                  // Container(
                  //   margin: EdgeInsets.only(right: 8, top: 2),
                  //   padding: EdgeInsets.all(4),
                  //   decoration: BoxDecoration(
                  //     color:
                  //         Colors.white.withValues(alpha: 179), // 0.7 * 255 = ~179
                  //     borderRadius: BorderRadius.circular(4),
                  //   ),
                  //   child:
                  //   //  Icon(
                  //   //   widget.chatTypeIcon,
                  //   //   size: 16,
                  //   //   color: Theme.of(context).colorScheme.primary,
                  //   // ),
                  // ),

                  // Chat content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title
                        Padding(
                          padding: EdgeInsets.only(left: AppSpacing.xs),
                          child: Text(
                            widget.chat['title'] ?? 'Untitled Chat',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 12,
                              // fontFamily: 'TiemposText',
                              color: Colors.black,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                        // SizedBox(height: 4),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        //  Divider(

        //               thickness: 1,
        //               color: Colors.grey.shade300,
        //             ),
      ],
    );
  }
}

// Relationship Entity Card widget
class _RelationshipEntityCard extends StatefulWidget {
  final Entity entity;
  final Entity parentEntity;
  final String relationshipType;
  final VoidCallback onTap;

  const _RelationshipEntityCard({
    required this.entity,
    required this.parentEntity,
    required this.relationshipType,
    required this.onTap,
  });

  @override
  State<_RelationshipEntityCard> createState() =>
      _RelationshipEntityCardState();
}

class _RelationshipEntityCardState extends State<_RelationshipEntityCard> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    // Get access to the parent state
    final _WebHomeScreenNewState? parentState =
        context.findAncestorStateOfType<_WebHomeScreenNewState>();

    // Create a unique key for this entity
    final GlobalKey entityCardKey =
        GlobalKey(debugLabel: 'relationshipEntity_${widget.entity.id}');

    return Column(
      children: [
        // Entity card with hover effect
        MouseRegion(
          onEnter: (_) => setState(() => isHovered = true),
          onExit: (_) => setState(() => isHovered = false),
          child: GestureDetector(
            onTap: widget.onTap,
            child: Container(
              key: entityCardKey,
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: isHovered ? Colors.grey.shade50 : Colors.white,
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade200),
                ),
              ),
              child: Row(
                children: [
                  // Checkbox
                  Container(
                    padding: EdgeInsets.only(
                      right: AppSpacing.xxs,
                      top: AppSpacing.xs,
                      bottom: AppSpacing.xs,
                    ),
                    decoration: BoxDecoration(
                      border: Border(
                        right:
                            BorderSide(color: Colors.grey.shade300, width: 1),
                      ),
                    ),
                    child: CustomCheckbox(
                      initialValue: parentState?.globalEntitiesData
                              .getEntityCheckedState(widget.entity.id ?? '') ??
                          false,
                      onChanged: (bool value) {
                        if (parentState != null) {
                          parentState.setState(() {
                            parentState.globalEntitiesData
                                .updateEntityCheckedState(
                                    widget.entity.id ?? '', value);
                          });
                        }
                      },
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                  SizedBox(width: AppSpacing.xs),

                  // Entity title and attributes
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title
                        Text(
                          widget.entity.title ?? 'Untitled',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 13,
                            fontFamily: "TiemposText",
                          ),
                        ),

                        // Attributes
                        if (widget.entity.attributeString != null &&
                            widget.entity.attributeString!.isNotEmpty)
                          Row(
                            children: [
                              Text(
                                'has ',
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey.shade700,
                                  fontFamily: "TiemposText",
                                  fontSize: 12,
                                ),
                              ),
                              Expanded(
                                child: parentState
                                        ?._buildAttributeStringWithPrimaryKeys(
                                      widget.entity,
                                      allowEllipsis: true,
                                    ) ??
                                    Text(widget.entity.attributeString ?? ''),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),

                  // Relationship indicator
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                    decoration: BoxDecoration(
                      color: parentState
                              ?.getRelationColor(widget.entity.relationType)
                              .withAlpha(25) ??
                          Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          parentState?.getRelationIcon(
                                  widget.entity.relationType) ??
                              Icons.link,
                          size: 12,
                          color: parentState?.getRelationColor(
                                  widget.entity.relationType) ??
                              Colors.grey.shade700,
                        ),
                        SizedBox(width: 3),
                        Text(
                          widget.entity.relationType?.toUpperCase() ?? '',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                            color: parentState?.getRelationColor(
                                    widget.entity.relationType) ??
                                Colors.grey.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// Custom expansion tile that handles ellipsis correctly
class _CustomExpansionTileWithEllipsis extends StatefulWidget {
  final Entity entity;
  final GlobalKey entityCardKey;
  final Function(bool) onExpansionChanged;
  final VoidCallback onTitleTap;
  final Color backgroundColor;
  final List<Widget> children;

  const _CustomExpansionTileWithEllipsis({
    required this.entity,
    required this.entityCardKey,
    required this.onExpansionChanged,
    required this.onTitleTap,
    this.backgroundColor = Colors.transparent,
    this.children = const [],
  });

  @override
  State<_CustomExpansionTileWithEllipsis> createState() =>
      _CustomExpansionTileWithEllipsisState();
}

class _CustomExpansionTileWithEllipsisState
    extends State<_CustomExpansionTileWithEllipsis> {
  late bool isExpanded;

  @override
  void initState() {
    super.initState();
    isExpanded = widget.entity.expanded ?? false;
  }

  @override
  void didUpdateWidget(_CustomExpansionTileWithEllipsis oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.entity.expanded != widget.entity.expanded) {
      isExpanded = widget.entity.expanded ?? false;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Log the expansion state for debugging
    Logger.info(
        "Building CustomExpansionTile with expanded: $isExpanded for entity: ${widget.entity.title}");

    return CustomExpansionTileNew(
      title: Builder(
        builder: (context) {
          // Show with ellipsis when collapsed, without ellipsis when expanded
          return _buildEntityTitle(
            widget.entityCardKey,
            widget.entity,
            isExpanded,
          );
        },
      ),
      initiallyExpanded: isExpanded,
      onExpansionChanged: (expanded) {
        setState(() {
          isExpanded = expanded;
        });
        widget.onExpansionChanged(expanded);
      },
      onTitleTap: widget.onTitleTap,
      backgroundColor: widget.backgroundColor,
      children: widget.children,
    );
  }

  // Build entity title with or without ellipsis
  Widget _buildEntityTitle(
      GlobalKey entityCardKey, Entity entity, bool expanded) {
    // Get access to the global entities data from the parent widget
    final _WebHomeScreenNewState? parentState =
        context.findAncestorStateOfType<_WebHomeScreenNewState>();
    final entitiesData = parentState?.globalEntitiesData ?? EntitiesData();

    return Row(
      key: entityCardKey,
      children: [
        Container(
          padding: EdgeInsets.only(
              left: 0,
              right: AppSpacing.xxs,
              top: AppSpacing.xs,
              bottom: AppSpacing.xs),
          decoration: BoxDecoration(
            border: Border(
                right: BorderSide(color: Colors.grey.shade300, width: 1)),
          ),
          child: CustomCheckbox(
            initialValue: entitiesData.getEntityCheckedState(entity.id ?? ''),
            onChanged: (bool value) {
              // Use a callback to update the parent state
              if (parentState != null) {
                parentState.setState(() {
                  entitiesData.updateEntityCheckedState(entity.id ?? '', value);
                });
              }
            },
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ),
        SizedBox(width: AppSpacing.xs),
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: AppSpacing.xs),
            child: Row(
              children: [
                // Create a combined RichText for the entire title with attributes
                Expanded(
                  child: Builder(
                    builder: (context) {
                      final GlobalKey titleKey =
                          GlobalKey(debugLabel: 'entityTitle_${entity.id}');

                      // Create a list of text spans for the combined title
                      List<InlineSpan> titleSpans = [];

                      // Add the entity title with bold style
                      TextSpan titleSpan = TextSpan(
                        text: entity.title ?? 'Untitled',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          fontFamily: "TiemposText",
                        ),
                      );

                      // Wrap only the title in MouseRegion for tooltip
                      titleSpans.add(WidgetSpan(
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          onEnter: (_) {
                            // Show profile tooltip only when hovering on title
                            if (parentState != null) {
                              parentState._showProfileTooltip(titleKey, entity);
                            }
                          },
                          onExit: (_) {
                            // Hide profile tooltip when not hovering
                            if (parentState != null) {
                              parentState._hideProfileTooltip();
                            }
                          },
                          child: Text.rich(
                            TextSpan(
                                text: titleSpan.text, style: titleSpan.style),
                          ),
                        ),
                      ));

                      // Add attributes if they exist
                      if (entity.attributeString != null &&
                          entity.attributeString!.isNotEmpty) {
                        // Add " has " text
                        titleSpans.add(
                          TextSpan(
                            text: ' has ',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.grey.shade700,
                              fontFamily: "TiemposText",
                            ),
                          ),
                        );

                        // Add attribute spans
                        titleSpans.addAll(_buildAttributeSpans(
                          entity.attributeString ?? '',
                          entity.attributes ?? [],
                        ));
                      }

                      // Log the RichText properties for debugging
                      Logger.info(
                          "RichText with expanded: $expanded, overflow: ${!expanded ? 'ellipsis' : 'visible'}, maxLines: ${!expanded ? 1 : 'null'}");

                      return RichText(
                        key: titleKey,
                        text: TextSpan(
                            children: titleSpans,
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: "TiemposText",
                              textBaseline: TextBaseline.alphabetic,
                            )),
                        overflow: !expanded
                            ? TextOverflow.ellipsis
                            : TextOverflow.visible,
                        softWrap: expanded,
                        maxLines: !expanded ? 1 : null,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Build attribute spans for the combined RichText
  List<InlineSpan> _buildAttributeSpans(
      String attributeString, List<attribute.Attribute> attributes) {
    // Log for debugging
    Logger.info("Building attribute spans for: $attributeString");

    if (attributeString.isEmpty) {
      return [];
    }

    // If there are no attributes, just return the original string as a span
    if (attributes.isEmpty) {
      return [
        TextSpan(
          text: attributeString,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
            color: Colors.black,
            fontFamily: 'TiemposText',
          ),
        ),
      ];
    }

    // Create a map of attribute names to their isPk status
    final Map<String, bool> primaryKeyMap = {};
    for (var attribute in attributes) {
      if (attribute.name != null) {
        primaryKeyMap[attribute.name!] = attribute.isPk ?? false;
      }
    }

    // Split the attribute string by commas and "and"
    List<String> parts = [];
    final andParts = attributeString.split(' and ');
    for (int i = 0; i < andParts.length; i++) {
      if (i < andParts.length - 1) {
        final commaParts = andParts[i].split(', ');
        parts.addAll(commaParts);
      } else {
        parts.add(andParts[i]);
      }
    }

    // Create a list of text spans for each attribute
    List<InlineSpan> textSpans = [];
    for (int i = 0; i < parts.length; i++) {
      String part = parts[i].trim();
      bool isPrimaryKey = false;

      for (var attrName in primaryKeyMap.keys) {
        if (part.contains(attrName) && primaryKeyMap[attrName] == true) {
          isPrimaryKey = true;
          break;
        }
      }

      // Add the attribute with or without PK indicator
      if (isPrimaryKey) {
        textSpans.add(
          TextSpan(
            text: part,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          ),
        );
        textSpans.add(TextSpan(text: ' '));
        textSpans.add(
          WidgetSpan(
            alignment: PlaceholderAlignment.top,
            child: Transform.translate(
              offset: Offset(0, -5),
              child: Text(
                '^PK',
                style: TextStyle(
                  fontSize: 9,
                  fontWeight: FontWeight.bold,
                  color: Color(0xff0058FF),
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
          ),
        );
      } else {
        textSpans.add(
          TextSpan(
            text: part,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          ),
        );
      }

      // Add separator if not the last item
      if (i < parts.length - 1) {
        if (i == parts.length - 2) {
          textSpans.add(TextSpan(text: ' and '));
        } else {
          textSpans.add(TextSpan(text: ', '));
        }
      }
    }

    return textSpans;
  }
}

class EntityProfileCard extends StatelessWidget {
  final String id;
  final String version;
  final String createdBy;
  final DateTime createdDate;
  final String modifiedBy;
  final DateTime modifiedDate;
  final Map<String, dynamic>? properties;
  final VoidCallback? onEdit;
  final String title;

  const EntityProfileCard({
    super.key,
    required this.id,
    required this.version,
    required this.createdBy,
    required this.createdDate,
    required this.modifiedBy,
    required this.modifiedDate,
    this.properties,
    this.onEdit,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    // Format dates for the UserProfileCard
    final String formattedCreatedDate = _formatDate(createdDate);
    final String formattedModifiedDate = _formatDate(modifiedDate);

    // Use our reusable UserProfileCard component
    return UserProfileCard(
      id: id,
      version: version,
      displayName: title,
      createdBy: createdBy,
      createdDate: formattedCreatedDate,
      modifiedBy: modifiedBy,
      modifiedDate: formattedModifiedDate,
      roleTitle: 'Entity Type',
      roleDescription:
          'Core Business Entity - Represents an individual or organisation that purchases goods.',

      width: MediaQuery.of(context).size.width / 3, // Match the original width
      leftMargin: 0, // No left margin needed in this context
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

class WorkflowProfileCard extends StatelessWidget {
  final String id;
  final String version;
  final String createdBy;
  final String createdDate;
  final String modifiedBy;
  final String modifiedDate;
  final Map<String, dynamic>? properties;
  final VoidCallback? onEdit;
  final String title;

  const WorkflowProfileCard({
    super.key,
    required this.id,
    required this.version,
    required this.createdBy,
    required this.createdDate,
    required this.modifiedBy,
    required this.modifiedDate,
    this.properties,
    this.onEdit,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    // Use our reusable UserProfileCard component
    return UserProfileCard(
      id: id,
      version: version,
      displayName: title,
      createdBy: createdBy,
      createdDate: createdDate,
      modifiedBy: modifiedBy,
      modifiedDate: modifiedDate,
      roleTitle: 'Workflow',
      roleDescription:
          'Core Business Process - Represents a sequence of steps for completing a business task.',
      profileImagePath: 'assets/images/workflow_icon.png',
      headerColor: Color(0xFF0058FF), // Blue header
      width: MediaQuery.of(context).size.width / 3, // Match the original width
      leftMargin: 0, // No left margin needed in this context
    );
  }
}

class _HoverArrowIcon extends StatefulWidget {
  final VoidCallback onTap;
  final Animation<double> iconTurn;

  const _HoverArrowIcon({
    required this.onTap,
    required this.iconTurn,
  });

  @override
  State<_HoverArrowIcon> createState() => _HoverArrowIconState();
}

class _HoverArrowIconState extends State<_HoverArrowIcon> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: InkWell(
        onTap: widget.onTap,
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: RotationTransition(
          turns: widget.iconTurn,
          child: SvgPicture.asset(
            'assets/images/arrow_down.svg',
            width: 24,
            height: 24,
            colorFilter: ColorFilter.mode(
              isHovered ? Color(0xff0058FF) : Colors.grey.shade600,
              BlendMode.srcIn,
            ),
          ),
        ),
      ),
    );
  }
}

class _AddButtonWithMenu extends StatefulWidget {
  final _WebHomeScreenNewState parentState;

  const _AddButtonWithMenu(this.parentState);

  @override
  State<_AddButtonWithMenu> createState() => _AddButtonWithMenuState();
}

class _AddButtonWithMenuState extends State<_AddButtonWithMenu> {
  bool isHovered = false;
  bool isMenuOpen = false;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  // File upload state
  bool isFileUploaded = false;
  String uploadedFileName = '';
  String extractedText = '';

  @override
  void dispose() {
    _removeMenu();
    super.dispose();
  }

  void _toggleMenu() {
    if (isMenuOpen) {
      _removeMenu();
    } else {
      _showMenu();
    }
  }

  void _removeMenu() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() {
      isMenuOpen = false;
    });
  }

  void _showMenu() {
    _removeMenu();

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    // Set a fixed menu height
    final menuHeight = 100; // Approximate height of the menu

    // Get screen height to determine if menu should appear above or below
    final screenHeight = MediaQuery.of(context).size.height;

    // Check if there's enough space below the button
    final spaceBelow = screenHeight - (offset.dy + size.height);
    final showAbove = spaceBelow < menuHeight + 20; // Add some buffer space

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx,
        // Position above or below based on available space, with minimal gap
        top: showAbove
            ? offset.dy - menuHeight + 15 // Position above with small gap
            : offset.dy + size.height - 5, // Position below with small gap
        child: Material(
          // elevation: 4.0,
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
          child: Container(
            width: 190,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppSpacing.xxs),
              border: Border.all(color: Color(0xffC1C1C1), width: 1),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AddButtonMenuItem(
                  imagePath: 'assets/images/upload_file.svg',
                  text: 'Upload A File',
                  onTap: () {
                    _removeMenu();
                    _uploadFile();
                  },
                ),
                AddButtonMenuItem(
                  imagePath: 'assets/images/screenshot.svg',
                  text: 'Take A Screenshot',
                  onTap: () {
                    _removeMenu();
                    // Handle screenshot action
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      isMenuOpen = true;
    });
  }

  // Upload file and process OCR
  Future<void> _uploadFile() async {
    // Create the service
    final fileUploadOcrService = FileUploadOcrService();

    // Store the context before async operation
    final BuildContext currentContext = context;

    // Show loading overlay
    _showLoadingOverlay(currentContext, 'Uploading file...');

    try {
      // Process the file - now the file upload API returns OCR data directly
      final result = await fileUploadOcrService.processFileForChat();

      // Hide loading overlay
      _hideLoadingOverlay();

      // Check if widget is still mounted before updating UI
      if (!mounted) return;

      if (result['success']) {
        // Update state with file info
        setState(() {
          isFileUploaded = true;
          uploadedFileName = result['fileName'];
          extractedText = result['extractedText'];
        });

        // Update parent state
        widget.parentState.setState(() {
          widget.parentState.uploadedFileName = result['fileName'];
          widget.parentState.uploadedFileText = result['extractedText'];
          widget.parentState.isFileUploaded = true;
        });

        // Show success message if still mounted
        if (mounted) {
          fileUploadOcrService.showOverlay(
            context,
            'File uploaded: ${result['fileName']}',
          );
        }

        // Log the extracted text
        Logger.info(
            'Extracted text to be sent with next message: ${result['extractedText']}');
      } else if (result['isCanceled'] != true) {
        // Show error message if not canceled and still mounted
        if (mounted) {
          fileUploadOcrService.showOverlay(
            context,
            result['errorMessage'] ?? 'File upload failed',
            isError: true,
          );
        }
      }
    } catch (e) {
      // Hide loading overlay
      _hideLoadingOverlay();

      // Show error message if still mounted
      if (mounted) {
        fileUploadOcrService.showOverlay(
          context,
          'Error: $e',
          isError: true,
        );
      }
    }
  }

  // Show loading overlay
  BuildContext? _loadingDialogContext;

  void _showLoadingOverlay(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        _loadingDialogContext = dialogContext;
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text(message),
            ],
          ),
        );
      },
    );
  }

  // Hide loading overlay
  void _hideLoadingOverlay() {
    if (_loadingDialogContext != null) {
      Navigator.of(_loadingDialogContext!).pop();
      _loadingDialogContext = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: MouseRegion(
        onEnter: (_) => setState(() => isHovered = true),
        onExit: (_) => setState(() => isHovered = false),
        cursor: SystemMouseCursors.click,
        child: Container(
          height: 28,
          width: 28,
          margin: EdgeInsets.symmetric(
              horizontal: AppSpacing.xs, vertical: AppSpacing.sm),
          padding: EdgeInsets.zero,
          decoration: BoxDecoration(
              color: isMenuOpen
                  ? Theme.of(context).colorScheme.primary
                  : isHovered
                      ? Theme.of(context).colorScheme.primary
                      : Color(0xffE4EDFF),
              borderRadius: BorderRadius.circular(AppSpacing.lg)),
          child: IconButton(
            padding: EdgeInsets.zero,
            onPressed: _toggleMenu,
            icon: Icon(isMenuOpen ? Icons.close : Icons.add),
            iconSize: 18,
            color: isMenuOpen || isHovered ? Colors.white : null,
          ),
        ),
      ),
    );
  }
}

// Custom painter for vertical dotted lines
class DottedLinePainter extends CustomPainter {
  final Color color;
  final double dashLength;
  final double dashGap;

  DottedLinePainter({
    required this.color,
    required this.dashLength,
    required this.dashGap,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    double startY = 0;
    while (startY < size.height) {
      // Draw a small dash
      canvas.drawLine(
        Offset(0, startY),
        Offset(0, startY + dashLength),
        paint,
      );
      // Move past the dash and gap
      startY += dashLength + dashGap;
    }
  }

  @override
  bool shouldRepaint(DottedLinePainter oldDelegate) {
    return oldDelegate.color != color ||
        oldDelegate.dashLength != dashLength ||
        oldDelegate.dashGap != dashGap;
  }
}

// Custom painter for horizontal dotted lines
class HorizontalDottedLinePainter extends CustomPainter {
  final Color color;
  final double dashLength;
  final double dashGap;

  HorizontalDottedLinePainter({
    required this.color,
    required this.dashLength,
    required this.dashGap,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    double startX = 0;
    while (startX < size.width) {
      // Draw a small dash
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashLength, 0),
        paint,
      );
      // Move past the dash and gap
      startX += dashLength + dashGap;
    }
  }

  @override
  bool shouldRepaint(HorizontalDottedLinePainter oldDelegate) {
    return oldDelegate.color != color ||
        oldDelegate.dashLength != dashLength ||
        oldDelegate.dashGap != dashGap;
  }
}

// Widget that wraps the entire message content with hover functionality
class _MessageContentWithHover extends StatefulWidget {
  final ChatMessage message;
  final int index;
  final AudioPlayer audioPlayer;
  final String? currentPlayingMessageId;
  final bool isPlaying;
  final bool isPaused;
  final Duration currentPosition;
  final Duration totalDuration;
  final Function(String, {String? messageId}) onTextToSpeech;
  final VoidCallback onStopAudio;
  final Function(BuildContext) showCopyOverlay;

  const _MessageContentWithHover({
    required this.message,
    required this.index,
    required this.audioPlayer,
    required this.currentPlayingMessageId,
    required this.isPlaying,
    required this.isPaused,
    required this.currentPosition,
    required this.totalDuration,
    required this.onTextToSpeech,
    required this.onStopAudio,
    required this.showCopyOverlay,
  });

  @override
  State<_MessageContentWithHover> createState() =>
      _MessageContentWithHoverState();
}

class _MessageContentWithHoverState extends State<_MessageContentWithHover> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    // Always show buttons if audio is playing for this message
    final String messageId = 'msg_${widget.index}';
    final bool alwaysShow =
        widget.currentPlayingMessageId == messageId && widget.isPlaying;

    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display file preview if this is a response to a file upload
          if (!widget.message.isUser && widget.message.fileData != null)
            FileUploadResponsePreview(
              fileName: widget.message.fileData!['fileName'] ?? 'Uploaded File',
            ),

          // Display text content if available
          if (widget.message.content.isNotEmpty)
            Text(
              widget.message.content,
              style: TextStyle(
                height: MediaQuery.of(context).size.width > 1600 ? 1.5 : 2,
                fontFamily: 'TiemposText',
                fontSize: MediaQuery.of(context).size.width > 1600 ? 17 : 15,
                fontWeight: FontWeight.w400,
                color: Colors.black,
              ),
            ),

          // Add spacing if both content and customContent are present
          if (widget.message.content.isNotEmpty &&
              widget.message.customContent != null)
            SizedBox(height: 16),

          // Display custom content if available
          if (widget.message.customContent != null)
            widget.message.customContent!,

          SizedBox(height: 8),

          // Message action buttons with hover functionality
          AnimatedOpacity(
            opacity: isHovered || alwaysShow ? 1.0 : 0.0,
            duration: Duration(milliseconds: 200),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.max,
              children: [
                MessageActionButtons(
                  // Use content for TTS and copy operations
                  // If content is empty but we have customContent, use a default message
                  messageContent: widget.message.content.isNotEmpty
                      ? widget.message.content
                      : "I've processed your request. Please see the information above.",
                  messageId: messageId,
                  audioPlayer: widget.audioPlayer,
                  currentPlayingMessageId: widget.currentPlayingMessageId,
                  isPlaying: widget.isPlaying,
                  isPaused: widget.isPaused,
                  currentPosition: widget.currentPosition,
                  totalDuration: widget.totalDuration,
                  onTextToSpeech: widget.onTextToSpeech,
                  onStopAudio: widget.onStopAudio,
                  showCopyOverlay: widget.showCopyOverlay,
                ),
              ],
            ),
          ),
          SizedBox(height: AppSpacing.xs)
        ],
      ),
    );
  }
}

class CutCornerClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    double cutSize = 20.0; // size of the cut corner

    final path = Path();
    path.moveTo(0, 0); // top-left
    path.lineTo(size.width - cutSize, 0); // move to just before top-right
    path.lineTo(size.width, cutSize); // diagonal cut
    path.lineTo(size.width, size.height); // right-bottom
    path.lineTo(0, size.height); // bottom-left
    path.close(); // back to top-left

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}