import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image_picker/image_picker.dart';

class CaptureImageWidget extends StatefulWidget {
  final double size;
  final bool isCircular;
  final bool useFrontCamera;
  final double imageQuality;
  final bool allowGallery;
  final String? resolution;
  final bool autoCapture;
  final String? buttonText;
  final bool showPreview;
  final String? displayName;
  final bool showDisplayName;
  final ValueChanged<File>? onImageSelected;
  final bool? testAutoOpen; // For testing purposes only
  final Uint8List? testImage; // For testing purposes only

  const CaptureImageWidget({
    super.key,
    this.size = 120,
    this.isCircular = true,
    this.useFrontCamera = false,
    this.imageQuality = 0.8,
    this.allowGallery = true,
    this.resolution,
    this.autoCapture = false,
    this.buttonText,
    this.showPreview = true,
    this.displayName,
    this.showDisplayName = true,
    this.onImageSelected,
    this.testAutoOpen,
    this.testImage,
  });

  /// Creates a CaptureImageWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the CaptureImageWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "size": 150,
  ///   "isCircular": true,
  ///   "useFrontCamera": true,
  ///   "imageQuality": 0.9,
  ///   "allowGallery": true,
  ///   "resolution": "1920x1080",
  ///   "autoCapture": false,
  ///   "buttonText": "Take Selfie",
  ///   "showPreview": true
  /// }
  /// ```
  factory CaptureImageWidget.fromJson(Map<String, dynamic> json) {
    return CaptureImageWidget(
      size: (json['size'] as num?)?.toDouble() ?? 120.0,
      isCircular: json['isCircular'] ?? true,
      useFrontCamera: json['useFrontCamera'] ?? false,
      imageQuality: (json['imageQuality'] as num?)?.toDouble() ?? 0.8,
      allowGallery: json['allowGallery'] ?? true,
      resolution: json['resolution'] as String?,
      autoCapture: json['autoCapture'] ?? false,
      buttonText: json['buttonText'] as String?,
      showPreview: json['showPreview'] ?? true,
      displayName: json['displayName'] as String?,
      showDisplayName: json['showDisplayName'] ?? true,
      onImageSelected: (file) {
        // This would be handled by the app in a real implementation
      },
    );
  }

  /// Converts the CaptureImageWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    return {
      'size': size,
      'isCircular': isCircular,
      'useFrontCamera': useFrontCamera,
      'imageQuality': imageQuality,
      'allowGallery': allowGallery,
      if (resolution != null) 'resolution': resolution,
      'autoCapture': autoCapture,
      if (buttonText != null) 'buttonText': buttonText,
      'showPreview': showPreview,
      if (displayName != null) 'displayName': displayName,
      'showDisplayName': showDisplayName,
    };
  }

  @override
  State<CaptureImageWidget> createState() => _CaptureImageWidgetState();
}

class _CaptureImageWidgetState extends State<CaptureImageWidget>
    with TickerProviderStateMixin {
  File? _imageFile;
  Uint8List? _webImage;
  bool _isHovering = false;
  bool _isImageHovering = false;
  bool _isActiveCamera = false;
  bool _isSaved = false;

  // Animation controller and animations for hover effects
  late AnimationController _hoverAnimationController;
  late Animation<double> _scaleAnimation;

  bool get _hasImage => kIsWeb ? _webImage != null : _imageFile != null;

  // Build the appropriate image widget based on platform and state
  Widget _buildImageWidget() {
    if (kIsWeb) {
      // Web platform
      if (_webImage != null) {
        return Image.memory(_webImage!, fit: BoxFit.cover);
      }
    } else {
      // Mobile platform
      if (_imageFile != null) {
        return Image.file(_imageFile!, fit: BoxFit.cover);
      }
    }

    // Default placeholder
    return Icon(
      widget.useFrontCamera ? Icons.face : Icons.camera_alt,
      size: widget.size / 3,
      color: Colors.black54,
    );
  }

  @override
  void initState() {
    super.initState();

    // Initialize animation controller for hover effects
    _hoverAnimationController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );

    // Create scale animation for hover effect
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _hoverAnimationController,
      curve: Curves.easeInOut,
    ));

    // Set test image if provided (for testing purposes)
    if (widget.testImage != null) {
      setState(() {
        _webImage = widget.testImage;
      });
    }

    // Auto-capture if enabled or test auto open is true
    if (widget.autoCapture || widget.testAutoOpen == true) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Directly open the camera without showing a dialog
        if (widget.testAutoOpen != true) {
          // Don't actually open camera in tests
          _openCameraScreen();
        }
      });
    }
  }

  @override
  void dispose() {
    _hoverAnimationController.dispose();
    super.dispose();
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      if (source == ImageSource.camera) {
        // Use our custom camera screen for direct camera access
        await _openCameraScreen();
      } else {
        // For gallery, use the standard image picker
        await _openGallery();
      }
    } catch (e) {
      // Handle errors silently
    }
  }

  Future<void> _openCameraScreen() async {
    try {
      // Configure image picker options
      final ImagePicker picker = ImagePicker();

      // Parse resolution if provided
      int? maxWidth;
      int? maxHeight;

      if (widget.resolution != null) {
        final resolutionParts = widget.resolution!.split('x');
        if (resolutionParts.length == 2) {
          maxWidth = int.tryParse(resolutionParts[0]);
          maxHeight = int.tryParse(resolutionParts[1]);
        }
      }

      // Convert quality from 0.0-1.0 to 0-100 scale
      final int quality = (widget.imageQuality * 100).round();

      // Set camera device based on preference
      final CameraDevice preferredCamera =
          widget.useFrontCamera ? CameraDevice.front : CameraDevice.rear;

      try {
        final XFile? picked = await picker.pickImage(
          source: ImageSource.camera,
          maxWidth: maxWidth?.toDouble(),
          maxHeight: maxHeight?.toDouble(),
          imageQuality: quality,
          preferredCameraDevice: preferredCamera,
        );

        if (picked != null) {
          if (kIsWeb) {
            // Handle web platform
            final bytes = await picked.readAsBytes();
            if (mounted) {
              setState(() {
                _webImage = bytes;
                _imageFile = null; // Clear file reference on web
                _isActiveCamera = true; // Show active camera state
                _isSaved = false;
              });
            }
          } else {
            // Handle mobile platforms
            if (mounted) {
              setState(() {
                _imageFile = File(picked.path);
                _webImage = null; // Clear web image
                _isActiveCamera = true; // Show active camera state
                _isSaved = false;
              });
              widget.onImageSelected?.call(_imageFile!);
            }
          }
        }
      } catch (e) {
        // This is likely a permission error
        if (mounted) {
          _showPermissionDeniedMessage();
        }
      }
    } catch (e) {
      // Handle other errors silently
    }
  }

  Future<void> _openGallery() async {
    try {
      // Configure image picker options
      final ImagePicker picker = ImagePicker();

      // Parse resolution if provided
      int? maxWidth;
      int? maxHeight;

      if (widget.resolution != null) {
        final resolutionParts = widget.resolution!.split('x');
        if (resolutionParts.length == 2) {
          maxWidth = int.tryParse(resolutionParts[0]);
          maxHeight = int.tryParse(resolutionParts[1]);
        }
      }

      // Convert quality from 0.0-1.0 to 0-100 scale
      final int quality = (widget.imageQuality * 100).round();

      final XFile? picked = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: maxWidth?.toDouble(),
        maxHeight: maxHeight?.toDouble(),
        imageQuality: quality,
      );

      if (picked != null) {
        if (kIsWeb) {
          // Handle web platform
          final bytes = await picked.readAsBytes();
          setState(() {
            _webImage = bytes;
            _imageFile = null; // Clear file reference on web
            _isActiveCamera = true; // Show active camera state
            _isSaved = false;
          });
        } else {
          // Handle mobile platforms
          setState(() {
            _imageFile = File(picked.path);
            _webImage = null; // Clear web image
            _isActiveCamera = true; // Show active camera state
            _isSaved = false;
          });
          widget.onImageSelected?.call(_imageFile!);
        }
      }
    } catch (e) {
      // This is likely a permission error
      _showPermissionDeniedMessage();
    }
  }

  void _showPermissionDeniedMessage() {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'Camera permission denied. Please enable camera access in your device settings.',
        ),
        duration: Duration(seconds: 3),
      ),
    );
  }

  void _saveImage() {
    setState(() {
      _isSaved = true;
      _isActiveCamera = false;
    });
  }

  void _deleteImage() {
    setState(() {
      _imageFile = null;
      _webImage = null;
      _isActiveCamera = false;
      _isSaved = false;
      _isImageHovering = false;
    });
  }

  void _closeActiveCamera() {
    setState(() {
      _isActiveCamera = false;
      if (_hasImage) {
        _isSaved = true;
      }
    });
  }

  void _openCameraMode() {
    setState(() {
      _isActiveCamera = true;
      _isSaved = false;
    });
  }

  double _getResponsiveContainerHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 244.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      return 240.5; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      return 232.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      return 224.0; // Small (768-1024px)
    } else {
      return 224.0; // Default for very small screens
    }
  }

   double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 16.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 14.0; // Large
    } else if (screenWidth >= 1280) {
      return 12.0; // Medium
    } else {
      return 12.0; // Default for very small screens
    }
  }

  @override
  Widget build(BuildContext context) {
    // State 1: Default - Small red camera icon
    if (!_hasImage && !_isActiveCamera) {
      return _buildDefaultState();
    }

    // State 2: Active Camera - Large preview with controls
    if (_isActiveCamera && _hasImage) {
      return _buildActiveCameraState();
    }

    // State 3: After Save - Clean image preview only
    if (_isSaved && _hasImage) {
      return _buildSavedState();
    }

    // Fallback to default
    return _buildDefaultState();
  }

  Widget _buildDefaultState() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display name
        if (widget.showDisplayName)
          Padding(
            padding: const EdgeInsets.only(bottom: 4.0),
            child: Text(
              widget.displayName ?? 'Capture Image',
              style: TextStyle(
                fontSize: _getResponsiveFontSize(context),
                fontWeight: FontWeight.w500,
                fontFamily: 'Inter',
              ),
            ),
          ),

        // Camera icon with animations
        MouseRegion(
          onEnter: (_) {
            setState(() => _isHovering = true);
            _hoverAnimationController.forward();
          },
          onExit: (_) {
            setState(() => _isHovering = false);
            _hoverAnimationController.reverse();
          },
          child: GestureDetector(
            onTap: () => _pickImage(ImageSource.camera),
            child: AnimatedBuilder(
              animation: _scaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 200),
                    transitionBuilder: (Widget child, Animation<double> animation) {
                      return FadeTransition(
                        opacity: animation,
                        child: ScaleTransition(
                          scale: animation,
                          child: child,
                        ),
                      );
                    },
                    child: SvgPicture.asset(
                      _isHovering
                          ? 'assets/images/capture-image.svg'
                          : 'assets/images/capture-image.svg',
                      key: ValueKey(_isHovering),
                      package: 'ui_controls_library',
                      fit: BoxFit.contain,
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActiveCameraState() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display name
        if (widget.showDisplayName)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              widget.displayName ?? 'Capture Image',
              style: TextStyle(
                fontSize: _getResponsiveFontSize(context),
                fontWeight: FontWeight.w500,
                fontFamily: 'Inter',
              ),
            ),
          ),

        // Active camera container
        Container(
          width: 294,
          height: _getResponsiveContainerHeight(context),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey.shade300, width: 1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Stack(
            children: [
              // Image preview
              Positioned(
                top: 36,
                left: 16,
                right: 16,
                bottom: 60,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: Container(
                    width: 262,
                    height: 148,
                    decoration: BoxDecoration(color: Colors.grey[200]),
                    child: _buildImageWidget(),
                  ),
                ),
              ),

              // Close button (X)
              Positioned(
                top: 8,
                right: 8,
                child: GestureDetector(
                  onTap: _closeActiveCamera,
                  child: SizedBox(
                    width: 24,
                    height: 24,
                    // decoration: const BoxDecoration(
                    //   color: Colors.black54,
                    //   shape: BoxShape.circle,
                    // ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.black,
                      size: 16,
                    ),
                  ),
                ),
              ),

              // Camera button at bottom
              Positioned(
                bottom: 8,
                left: 0,
                right: 0,
                child: Center(
                  child: GestureDetector(
                    onTap: () => _pickImage(ImageSource.camera),
                    child: Container(
                      width: 44,
                      height: 44,
                      decoration: const BoxDecoration(
                        color: Color(0xFF0058FF),
                        shape: BoxShape.circle,
                      ),
                      child: SvgPicture.asset(
                        'assets/images/capture-image.svg',
                        package: 'ui_controls_library',
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSavedState() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display name
        if (widget.showDisplayName)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              widget.displayName ?? 'Capture Image',
              style: TextStyle(
                fontSize: _getResponsiveFontSize(context),
                fontWeight: FontWeight.w500,
                fontFamily: 'Inter',
              ),
            ),
          ),

        // Saved image container
        MouseRegion(
          onEnter: (_) => setState(() => _isImageHovering = true),
          onExit: (_) => setState(() => _isImageHovering = false),
          child: Container(
            width: 294,
            height: 166,
            // _getResponsiveContainerHeight(context),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(20),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Stack(
              children: [
                // Image
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: SizedBox(child: _buildImageWidget()),
                ),

                // Delete button on hover
                if (_isImageHovering)
                  Positioned(
                    top: 4,
                    right: 4,
                    child: GestureDetector(
                      onTap: _deleteImage,
                      child: Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(150),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.delete,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
